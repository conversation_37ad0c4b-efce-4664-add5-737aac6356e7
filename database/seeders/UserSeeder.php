<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear usuario administrador
        $admin = User::create([
            'name' => 'Administrador',
            'username' => 'xelk',
            'email' => '<EMAIL>',
            'password' => Hash::make('1234'),
            'locale' => 'es',
            'email_verified_at' => now(),
        ]);

        // Asignar rol de Super Admin si existe
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $admin->assignRole($superAdminRole);
        }

        // Crear usuario de ejemplo
        $user = User::create([
            'name' => 'Usuario Demo',
            'username' => 'demo',
            'email' => '<EMAIL>',
            'password' => Hash::make('1234'),
            'locale' => 'es',
            'email_verified_at' => now(),
        ]);

        // Crear algunos usuarios adicionales usando factory
        User::factory(5)->create();
    }
}

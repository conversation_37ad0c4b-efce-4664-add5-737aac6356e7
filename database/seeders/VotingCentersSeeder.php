<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\VotingCenter;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

class VotingCentersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🇻🇪 Iniciando importación de centros de votación de Venezuela...');
        
        // Leer archivo CSV
        $csvFile = database_path('seeders/data/voting_centers.csv');
        
        if (!file_exists($csvFile)) {
            $this->command->error("❌ Archivo CSV no encontrado: {$csvFile}");
            return;
        }
        
        $this->command->info("📄 Leyendo archivo: {$csvFile}");
        
        // Leer y procesar CSV
        $handle = fopen($csvFile, 'r');
        $header = fgetcsv($handle); // Leer encabezados
        
        $totalRows = 0;
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;
        
        // Crear mapas de ubicaciones para optimizar búsquedas
        $stateMap = $this->createStateMap();
        $municipalityMap = $this->createMunicipalityMap();
        $parishMap = $this->createParishMap();
        
        $this->command->info("🗺️  Mapas de ubicaciones creados:");
        $this->command->info("   - Estados: " . count($stateMap));
        $this->command->info("   - Municipios: " . count($municipalityMap));
        $this->command->info("   - Parroquias: " . count($parishMap));
        
        // Procesar cada fila del CSV
        while (($row = fgetcsv($handle)) !== false) {
            $totalRows++;
            
            try {
                $data = array_combine($header, $row);
                
                // Validar datos requeridos
                if (empty($data['code']) || empty($data['name'])) {
                    $this->command->warn("⚠️  Fila {$totalRows}: Datos requeridos faltantes (code o name)");
                    $skippedCount++;
                    continue;
                }
                
                // Verificar si ya existe
                if (VotingCenter::where('code', $data['code'])->exists()) {
                    $this->command->info("⏭️  Centro {$data['code']} ya existe, omitiendo...");
                    $skippedCount++;
                    continue;
                }
                
                // Buscar IDs de ubicaciones
                $locationIds = $this->findLocationIds(
                    $data,
                    $stateMap,
                    $municipalityMap,
                    $parishMap
                );
                
                if (!$locationIds) {
                    $this->command->warn("⚠️  Fila {$totalRows}: No se pudieron encontrar las ubicaciones para {$data['code']}");
                    $errorCount++;
                    continue;
                }
                
                // Crear centro de votación
                $centerData = [
                    'code' => $data['code'],
                    'old_code' => !empty($data['old_code']) ? $data['old_code'] : null,
                    'name' => $data['name'],
                    'address' => !empty($data['address']) ? $data['address'] : null,
                    'state_id' => $locationIds['state_id'],
                    'municipality_id' => $locationIds['municipality_id'],
                    'parish_id' => $locationIds['parish_id'],
                    'total_voters' => (int) ($data['total_voters'] ?? 0),
                    'total_tables' => (int) ($data['total_tables'] ?? 0),
                    'status' => $data['status'] ?? 'active',
                    'latitude' => !empty($data['latitude']) ? (float) $data['latitude'] : null,
                    'longitude' => !empty($data['longitude']) ? (float) $data['longitude'] : null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                
                VotingCenter::create($centerData);
                $successCount++;
                
                if ($successCount % 50 === 0) {
                    $this->command->info("✅ Procesados: {$successCount} centros");
                }
                
            } catch (\Exception $e) {
                $this->command->error("❌ Error en fila {$totalRows}: " . $e->getMessage());
                $errorCount++;
                Log::error("Error importando centro de votación", [
                    'row' => $totalRows,
                    'data' => $data ?? null,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        fclose($handle);
        
        // Mostrar resumen
        $this->command->info("\n📊 RESUMEN DE IMPORTACIÓN:");
        $this->command->info("   📄 Total de filas procesadas: {$totalRows}");
        $this->command->info("   ✅ Centros creados exitosamente: {$successCount}");
        $this->command->info("   ⏭️  Centros omitidos (ya existían): {$skippedCount}");
        $this->command->info("   ❌ Errores: {$errorCount}");
        
        if ($successCount > 0) {
            $this->command->info("\n🎉 ¡Importación de centros de votación completada exitosamente!");
        }
    }
    
    /**
     * Crear mapa de estados para búsqueda rápida
     */
    private function createStateMap(): array
    {
        $states = State::all();
        $map = [];
        
        foreach ($states as $state) {
            // Múltiples variaciones del nombre para mejor matching
            $variations = [
                $state->name,
                strtoupper($state->name),
                str_replace(['EDO. ', 'DTTO. '], '', strtoupper($state->name)),
            ];
            
            foreach ($variations as $variation) {
                $map[$variation] = $state->id;
            }
        }
        
        return $map;
    }
    
    /**
     * Crear mapa de municipios para búsqueda rápida
     */
    private function createMunicipalityMap(): array
    {
        $municipalities = Municipality::with('state')->get();
        $map = [];
        
        foreach ($municipalities as $municipality) {
            $key = $municipality->state->name . '|' . $municipality->name;
            $map[$key] = $municipality->id;
            
            // También crear variaciones
            $stateVariations = [
                $municipality->state->name,
                str_replace(['EDO. ', 'DTTO. '], '', $municipality->state->name),
            ];
            
            $municipalityVariations = [
                $municipality->name,
                str_replace('MP. ', '', $municipality->name),
            ];
            
            foreach ($stateVariations as $stateVar) {
                foreach ($municipalityVariations as $munVar) {
                    $map[$stateVar . '|' . $munVar] = $municipality->id;
                }
            }
        }
        
        return $map;
    }
    
    /**
     * Crear mapa de parroquias para búsqueda rápida
     */
    private function createParishMap(): array
    {
        $parishes = Parish::with(['municipality.state'])->get();
        $map = [];
        
        foreach ($parishes as $parish) {
            $key = $parish->municipality->state->name . '|' . 
                   $parish->municipality->name . '|' . 
                   $parish->name;
            $map[$key] = $parish->id;
            
            // También crear variaciones
            $parishVariations = [
                $parish->name,
                str_replace('PQ. ', '', $parish->name),
            ];
            
            foreach ($parishVariations as $parishVar) {
                $varKey = $parish->municipality->state->name . '|' . 
                         $parish->municipality->name . '|' . 
                         $parishVar;
                $map[$varKey] = $parish->id;
            }
        }
        
        return $map;
    }
    
    /**
     * Encontrar IDs de ubicaciones basado en los nombres
     */
    private function findLocationIds($data, $stateMap, $municipalityMap, $parishMap): ?array
    {
        $stateName = $data['state_name'] ?? '';
        $municipalityName = $data['municipality_name'] ?? '';
        $parishName = $data['parish_name'] ?? '';
        
        // Buscar estado
        $stateId = $stateMap[$stateName] ?? null;
        if (!$stateId) {
            // Intentar con variaciones
            $stateId = $stateMap[str_replace(['EDO. ', 'DTTO. '], '', $stateName)] ?? null;
        }
        
        if (!$stateId) {
            return null;
        }
        
        // Buscar municipio
        $municipalityKey = $stateName . '|' . $municipalityName;
        $municipalityId = $municipalityMap[$municipalityKey] ?? null;
        
        if (!$municipalityId) {
            // Intentar con variaciones
            $cleanStateName = str_replace(['EDO. ', 'DTTO. '], '', $stateName);
            $cleanMunicipalityName = str_replace('MP. ', '', $municipalityName);
            $municipalityKey = $cleanStateName . '|' . $cleanMunicipalityName;
            $municipalityId = $municipalityMap[$municipalityKey] ?? null;
        }
        
        if (!$municipalityId) {
            return null;
        }
        
        // Buscar parroquia
        $parishKey = $stateName . '|' . $municipalityName . '|' . $parishName;
        $parishId = $parishMap[$parishKey] ?? null;
        
        if (!$parishId) {
            // Intentar con variaciones
            $cleanParishName = str_replace('PQ. ', '', $parishName);
            $parishKey = $stateName . '|' . $municipalityName . '|' . $cleanParishName;
            $parishId = $parishMap[$parishKey] ?? null;
        }
        
        if (!$parishId) {
            return null;
        }
        
        return [
            'state_id' => $stateId,
            'municipality_id' => $municipalityId,
            'parish_id' => $parishId,
        ];
    }
}

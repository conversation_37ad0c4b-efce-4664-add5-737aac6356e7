<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('voting_centers', function (Blueprint $table) {
            // Agregar nuevas columnas si no existen
            if (!Schema::hasColumn('voting_centers', 'code')) {
                $table->string('code', 20)->unique()->after('id')->comment('Código único del centro de votación (ej: 010101001)');
            }
            
            if (!Schema::hasColumn('voting_centers', 'old_code')) {
                $table->string('old_code', 20)->nullable()->after('code')->comment('Código anterior del centro');
            }
            
            if (!Schema::hasColumn('voting_centers', 'state_id')) {
                $table->foreignId('state_id')->nullable()->after('parish_id')->constrained('states')->onDelete('cascade');
            }
            
            if (!Schema::hasColumn('voting_centers', 'municipality_id')) {
                $table->foreignId('municipality_id')->nullable()->after('state_id')->constrained('municipalities')->onDelete('cascade');
            }
            
            if (!Schema::hasColumn('voting_centers', 'total_voters')) {
                $table->integer('total_voters')->default(0)->after('address')->comment('Total de electores registrados en el centro');
            }
            
            if (!Schema::hasColumn('voting_centers', 'total_tables')) {
                $table->integer('total_tables')->default(0)->after('total_voters')->comment('Número total de mesas electorales');
            }
            
            if (!Schema::hasColumn('voting_centers', 'status')) {
                $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->after('total_tables');
            }
            
            if (!Schema::hasColumn('voting_centers', 'latitude')) {
                $table->decimal('latitude', 10, 8)->nullable()->after('status')->comment('Latitud GPS del centro');
            }
            
            if (!Schema::hasColumn('voting_centers', 'longitude')) {
                $table->decimal('longitude', 11, 8)->nullable()->after('latitude')->comment('Longitud GPS del centro');
            }
            
            if (!Schema::hasColumn('voting_centers', 'additional_info')) {
                $table->json('additional_info')->nullable()->after('longitude')->comment('Información adicional en formato JSON');
            }
        });

        // Agregar índices para optimizar consultas
        try {
            Schema::table('voting_centers', function (Blueprint $table) {
                $table->index(['state_id', 'municipality_id', 'parish_id'], 'voting_centers_state_municipality_parish_index');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }

        try {
            Schema::table('voting_centers', function (Blueprint $table) {
                $table->index('status', 'voting_centers_status_index');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }

        try {
            Schema::table('voting_centers', function (Blueprint $table) {
                $table->index('code', 'voting_centers_code_index');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('voting_centers', function (Blueprint $table) {
            // Eliminar índices
            $table->dropIndex('voting_centers_state_municipality_parish_index');
            $table->dropIndex('voting_centers_status_index');
            $table->dropIndex('voting_centers_code_index');
            
            // Eliminar columnas agregadas
            $table->dropColumn([
                'code',
                'old_code',
                'state_id',
                'municipality_id',
                'total_voters',
                'total_tables',
                'status',
                'latitude',
                'longitude',
                'additional_info'
            ]);
        });
    }
};

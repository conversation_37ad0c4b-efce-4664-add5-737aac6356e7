<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\VotingCenter;
use App\Models\State;
use App\Http\Controllers\Api\ElectoralApiController;
use App\Http\Controllers\ReportsController;
use Illuminate\Http\Request;

class TestElectoralSystem extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'electoral:test 
                            {--feature= : Funcionalidad específica a probar (api, reports, search, nearby)}
                            {--cedula= : Cédula para probar búsqueda}
                            {--lat= : Latitud para búsqueda cercana}
                            {--lng= : Longitud para búsqueda cercana}';

    /**
     * The description of the console command.
     */
    protected $description = 'Probar funcionalidades del sistema electoral';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🇻🇪 PROBANDO SISTEMA ELECTORAL VENEZOLANO');
        $this->newLine();

        $feature = $this->option('feature');

        switch ($feature) {
            case 'api':
                $this->testElectoralApi();
                break;
            case 'reports':
                $this->testReports();
                break;
            case 'search':
                $this->testAdvancedSearch();
                break;
            case 'nearby':
                $this->testNearbySearch();
                break;
            default:
                $this->testAllFeatures();
        }
    }

    /**
     * Test all features
     */
    private function testAllFeatures()
    {
        $this->info('🧪 EJECUTANDO TODAS LAS PRUEBAS');
        $this->newLine();

        $this->testBasicStats();
        $this->testElectoralApi();
        $this->testAdvancedSearch();
        $this->testNearbySearch();
        $this->testReports();
        
        $this->newLine();
        $this->info('✅ TODAS LAS PRUEBAS COMPLETADAS');
    }

    /**
     * Test basic statistics
     */
    private function testBasicStats()
    {
        $this->info('📊 PROBANDO ESTADÍSTICAS BÁSICAS');
        
        $totalCenters = VotingCenter::count();
        $activeCenters = VotingCenter::where('status', 'active')->count();
        $totalVoters = VotingCenter::sum('total_voters');
        $totalTables = VotingCenter::sum('total_tables');
        $withCoordinates = VotingCenter::whereNotNull('latitude')->whereNotNull('longitude')->count();
        
        $this->line("   📍 Total de centros: " . number_format($totalCenters));
        $this->line("   ✅ Centros activos: " . number_format($activeCenters));
        $this->line("   👥 Total de votantes: " . number_format($totalVoters));
        $this->line("   🗳️  Total de mesas: " . number_format($totalTables));
        $this->line("   🗺️  Con coordenadas: " . number_format($withCoordinates) . " (" . round(($withCoordinates/$totalCenters)*100, 1) . "%)");
        
        // Top 5 estados
        $this->line("\n   🏆 TOP 5 ESTADOS:");
        $topStates = VotingCenter::join('states', 'voting_centers.state_id', '=', 'states.id')
            ->select('states.name')
            ->selectRaw('COUNT(*) as total')
            ->groupBy('states.id', 'states.name')
            ->orderBy('total', 'desc')
            ->limit(5)
            ->get();
            
        foreach ($topStates as $index => $state) {
            $this->line("      " . ($index + 1) . ". {$state->name}: " . number_format($state->total) . " centros");
        }
        
        $this->newLine();
    }

    /**
     * Test electoral API
     */
    private function testElectoralApi()
    {
        $this->info('🔍 PROBANDO API ELECTORAL');
        
        try {
            $controller = new ElectoralApiController();
            
            // Test citizen info
            $cedula = $this->option('cedula') ?? '12345678';
            $this->line("   🆔 Probando consulta por cédula: {$cedula}");
            
            $request = new Request(['include_center' => true, 'include_location' => true]);
            $response = $controller->getCitizenInfo($request, $cedula);
            $data = json_decode($response->getContent(), true);
            
            if ($data['success']) {
                $center = $data['data']['voting_center'];
                $this->line("      ✅ Centro encontrado: {$center['name']}");
                $this->line("      📍 Ubicación: {$data['data']['location']['parish']}, {$data['data']['location']['municipality']}, {$data['data']['location']['state']}");
                $this->line("      🗳️  Mesa: {$data['data']['table_number']}");
            } else {
                $this->warn("      ❌ No se encontró información para la cédula");
            }
            
            // Test area search
            $this->line("\n   🗺️  Probando búsqueda por área (Distrito Capital):");
            $dcState = State::where('name', 'Distrito Capital')->first();
            if ($dcState) {
                $request = new Request(['state_id' => $dcState->id, 'include_stats' => true]);
                $response = $controller->getCentersByArea($request);
                $data = json_decode($response->getContent(), true);
                
                if ($data['success']) {
                    $stats = $data['statistics'] ?? [];
                    $this->line("      ✅ Centros encontrados: " . number_format($stats['total_centers'] ?? 0));
                    $this->line("      👥 Total votantes: " . number_format($stats['total_voters'] ?? 0));
                }
            }
            
        } catch (\Exception $e) {
            $this->error("   ❌ Error en API Electoral: " . $e->getMessage());
        }
        
        $this->newLine();
    }

    /**
     * Test advanced search
     */
    private function testAdvancedSearch()
    {
        $this->info('🔎 PROBANDO BÚSQUEDA AVANZADA');
        
        try {
            $controller = new ElectoralApiController();
            
            // Test search by name
            $this->line("   📝 Búsqueda por nombre 'SIMÓN BOLÍVAR':");
            $request = new Request(['q' => 'SIMÓN BOLÍVAR', 'per_page' => 5]);
            $response = $controller->searchCenters($request);
            $data = json_decode($response->getContent(), true);
            
            if ($data['success']) {
                $centers = $data['data']['data'];
                $this->line("      ✅ Encontrados: " . count($centers) . " centros");
                foreach (array_slice($centers, 0, 3) as $center) {
                    $this->line("         - {$center['name']} ({$center['state']['name']})");
                }
            }
            
            // Test search by voters range
            $this->line("\n   👥 Búsqueda por rango de votantes (2000-5000):");
            $request = new Request(['min_voters' => 2000, 'max_voters' => 5000, 'per_page' => 5]);
            $response = $controller->searchCenters($request);
            $data = json_decode($response->getContent(), true);
            
            if ($data['success']) {
                $centers = $data['data']['data'];
                $this->line("      ✅ Encontrados: " . count($centers) . " centros");
                foreach (array_slice($centers, 0, 3) as $center) {
                    $this->line("         - {$center['name']}: " . number_format($center['total_voters']) . " votantes");
                }
            }
            
        } catch (\Exception $e) {
            $this->error("   ❌ Error en búsqueda avanzada: " . $e->getMessage());
        }
        
        $this->newLine();
    }

    /**
     * Test nearby search
     */
    private function testNearbySearch()
    {
        $this->info('📍 PROBANDO BÚSQUEDA POR PROXIMIDAD');
        
        try {
            $controller = new ElectoralApiController();
            
            // Coordenadas de Caracas por defecto
            $lat = $this->option('lat') ?? 10.5061;
            $lng = $this->option('lng') ?? -66.9146;
            
            $this->line("   🗺️  Buscando centros cerca de ({$lat}, {$lng}):");
            
            $request = new Request([
                'latitude' => $lat,
                'longitude' => $lng,
                'radius' => 10,
                'limit' => 5,
                'include_distance' => true
            ]);
            
            $response = $controller->getNearbyVotingCenters($request);
            $data = json_decode($response->getContent(), true);
            
            if ($data['success']) {
                $centers = $data['data'];
                $this->line("      ✅ Encontrados: " . count($centers) . " centros en 10km");
                
                foreach ($centers as $center) {
                    $distance = $center['distance_km'] ?? 'N/A';
                    $this->line("         - {$center['name']}: {$distance}km");
                }
            } else {
                $this->warn("      ❌ No se encontraron centros cercanos");
            }
            
        } catch (\Exception $e) {
            $this->error("   ❌ Error en búsqueda por proximidad: " . $e->getMessage());
        }
        
        $this->newLine();
    }

    /**
     * Test reports
     */
    private function testReports()
    {
        $this->info('📊 PROBANDO SISTEMA DE REPORTES');
        
        try {
            $controller = new ReportsController();
            
            // Test electoral report
            $this->line("   📋 Generando reporte electoral:");
            $request = new Request(['include_details' => false]);
            $response = $controller->electoralReport($request);
            $data = json_decode($response->getContent(), true);
            
            if ($data['success']) {
                $summary = $data['data']['summary'];
                $this->line("      ✅ Reporte generado exitosamente");
                $this->line("         - Total centros: " . number_format($summary['total_centers']));
                $this->line("         - Centros activos: " . number_format($summary['active_centers']));
                $this->line("         - Total votantes: " . number_format($summary['total_voters']));
            }
            
            // Test capacity report
            $this->line("\n   ⚖️  Generando análisis de capacidad:");
            $request = new Request(['threshold_voters_per_table' => 400]);
            $response = $controller->capacityReport($request);
            $data = json_decode($response->getContent(), true);
            
            if ($data['success']) {
                $analysis = $data['data'];
                $this->line("      ✅ Análisis completado");
                $this->line("         - Centros sobrecargados: " . count($analysis['overcrowded']));
                $this->line("         - Centros con baja capacidad: " . count($analysis['undercapacity']));
                $this->line("         - Centros óptimos: " . count($analysis['optimal']));
                $this->line("         - Promedio votantes/mesa: " . $analysis['statistics']['average_voters_per_table']);
            }
            
            // Test geographic report
            $this->line("\n   🗺️  Generando reporte geográfico:");
            $request = new Request(['include_missing_coordinates' => false]);
            $response = $controller->geographicReport($request);
            $data = json_decode($response->getContent(), true);
            
            if ($data['success']) {
                $coverage = $data['data']['coverage_summary'];
                $this->line("      ✅ Reporte geográfico generado");
                $this->line("         - Cobertura de coordenadas: " . $coverage['coverage_percentage'] . "%");
                $this->line("         - Con coordenadas: " . number_format($coverage['with_coordinates']));
                $this->line("         - Sin coordenadas: " . number_format($coverage['without_coordinates']));
            }
            
        } catch (\Exception $e) {
            $this->error("   ❌ Error en reportes: " . $e->getMessage());
        }
        
        $this->newLine();
    }
}

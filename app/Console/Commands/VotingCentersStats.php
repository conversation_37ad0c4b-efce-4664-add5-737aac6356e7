<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\VotingCenter;
use App\Models\State;

class VotingCentersStats extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'voting-centers:stats 
                            {--state= : Mostrar estadísticas de un estado específico}
                            {--export= : Exportar estadísticas a archivo (csv, json)}';

    /**
     * The description of the console command.
     */
    protected $description = 'Mostrar estadísticas de centros de votación';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📊 ESTADÍSTICAS DE CENTROS DE VOTACIÓN DE VENEZUELA');
        $this->newLine();

        $stateFilter = $this->option('state');
        $exportFormat = $this->option('export');

        if ($stateFilter) {
            $this->showStateStats($stateFilter);
        } else {
            $this->showGeneralStats();
        }

        if ($exportFormat) {
            $this->exportStats($exportFormat);
        }
    }

    /**
     * Mostrar estadísticas generales
     */
    private function showGeneralStats()
    {
        $total = VotingCenter::count();
        
        if ($total === 0) {
            $this->warn('❌ No hay centros de votación en la base de datos');
            $this->info('💡 Ejecuta: php artisan db:seed --class=VotingCentersSeeder');
            return;
        }

        $this->info("🇻🇪 RESUMEN NACIONAL");
        $this->info("📍 Total de centros: " . number_format($total));
        
        // Totales de votantes y mesas
        $totals = VotingCenter::select(
            DB::raw('sum(total_voters) as total_voters'),
            DB::raw('sum(total_tables) as total_tables'),
            DB::raw('avg(total_voters) as avg_voters'),
            DB::raw('avg(total_tables) as avg_tables')
        )->first();
        
        $this->info("👥 Total de votantes: " . number_format($totals->total_voters));
        $this->info("🗳️  Total de mesas: " . number_format($totals->total_tables));
        $this->info("📊 Promedio votantes/centro: " . number_format($totals->avg_voters, 0));
        $this->info("📊 Promedio mesas/centro: " . number_format($totals->avg_tables, 1));
        
        $this->newLine();
        
        // Estadísticas por estado
        $this->info("📊 CENTROS POR ESTADO:");
        $stateStats = VotingCenter::join('states', 'voting_centers.state_id', '=', 'states.id')
            ->select(
                'states.name',
                DB::raw('count(*) as total_centers'),
                DB::raw('sum(total_voters) as total_voters'),
                DB::raw('sum(total_tables) as total_tables')
            )
            ->groupBy('states.id', 'states.name')
            ->orderBy('total_centers', 'desc')
            ->get();
            
        $this->table(
            ['Estado', 'Centros', 'Votantes', 'Mesas'],
            $stateStats->map(function ($stat) {
                return [
                    $stat->name,
                    number_format($stat->total_centers),
                    number_format($stat->total_voters),
                    number_format($stat->total_tables)
                ];
            })
        );
        
        // Estadísticas por status
        $this->newLine();
        $this->info("📊 CENTROS POR STATUS:");
        $statusStats = VotingCenter::select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->get();
            
        foreach ($statusStats as $stat) {
            $percentage = ($stat->total / $total) * 100;
            $this->line("   {$stat->status}: {$stat->total} centros (" . number_format($percentage, 1) . "%)");
        }
        
        // Top 10 centros con más votantes
        $this->newLine();
        $this->info("🏆 TOP 10 CENTROS CON MÁS VOTANTES:");
        $topCenters = VotingCenter::with(['state', 'municipality', 'parish'])
            ->orderBy('total_voters', 'desc')
            ->limit(10)
            ->get();
            
        foreach ($topCenters as $index => $center) {
            $this->line(sprintf(
                "   %d. %s (%s, %s) - %s votantes",
                $index + 1,
                $center->name,
                $center->municipality->name,
                $center->state->name,
                number_format($center->total_voters)
            ));
        }
    }

    /**
     * Mostrar estadísticas de un estado específico
     */
    private function showStateStats($stateName)
    {
        $state = State::where('name', 'like', "%{$stateName}%")->first();
        
        if (!$state) {
            $this->error("❌ Estado no encontrado: {$stateName}");
            return;
        }

        $this->info("📍 ESTADÍSTICAS DE {$state->name}");
        $this->newLine();

        $centers = VotingCenter::where('state_id', $state->id)->get();
        
        if ($centers->isEmpty()) {
            $this->warn("❌ No hay centros de votación registrados para {$state->name}");
            return;
        }

        $total = $centers->count();
        $totalVoters = $centers->sum('total_voters');
        $totalTables = $centers->sum('total_tables');
        
        $this->info("📍 Total de centros: " . number_format($total));
        $this->info("👥 Total de votantes: " . number_format($totalVoters));
        $this->info("🗳️  Total de mesas: " . number_format($totalTables));
        $this->info("📊 Promedio votantes/centro: " . number_format($totalVoters / $total, 0));
        $this->info("📊 Promedio mesas/centro: " . number_format($totalTables / $total, 1));
        
        $this->newLine();
        
        // Centros por municipio
        $this->info("📊 CENTROS POR MUNICIPIO:");
        $municipalityStats = VotingCenter::join('municipalities', 'voting_centers.municipality_id', '=', 'municipalities.id')
            ->where('voting_centers.state_id', $state->id)
            ->select(
                'municipalities.name',
                DB::raw('count(*) as total_centers'),
                DB::raw('sum(total_voters) as total_voters'),
                DB::raw('sum(total_tables) as total_tables')
            )
            ->groupBy('municipalities.id', 'municipalities.name')
            ->orderBy('total_centers', 'desc')
            ->get();
            
        $this->table(
            ['Municipio', 'Centros', 'Votantes', 'Mesas'],
            $municipalityStats->map(function ($stat) {
                return [
                    $stat->name,
                    number_format($stat->total_centers),
                    number_format($stat->total_voters),
                    number_format($stat->total_tables)
                ];
            })
        );
    }

    /**
     * Exportar estadísticas
     */
    private function exportStats($format)
    {
        $this->newLine();
        $this->info("📤 Exportando estadísticas en formato {$format}...");
        
        $data = $this->getExportData();
        $filename = "voting_centers_stats_" . date('Y-m-d_H-i-s') . ".{$format}";
        $filepath = storage_path("app/{$filename}");
        
        switch ($format) {
            case 'csv':
                $this->exportToCsv($data, $filepath);
                break;
            case 'json':
                $this->exportToJson($data, $filepath);
                break;
            default:
                $this->error("❌ Formato no soportado: {$format}");
                return;
        }
        
        $this->info("✅ Estadísticas exportadas a: {$filepath}");
    }

    /**
     * Obtener datos para exportar
     */
    private function getExportData()
    {
        return VotingCenter::with(['state', 'municipality', 'parish'])
            ->select([
                'code',
                'name',
                'address',
                'state_id',
                'municipality_id',
                'parish_id',
                'total_voters',
                'total_tables',
                'status',
                'latitude',
                'longitude'
            ])
            ->get()
            ->map(function ($center) {
                return [
                    'codigo' => $center->code,
                    'nombre' => $center->name,
                    'direccion' => $center->address,
                    'estado' => $center->state->name,
                    'municipio' => $center->municipality->name,
                    'parroquia' => $center->parish->name,
                    'total_votantes' => $center->total_voters,
                    'total_mesas' => $center->total_tables,
                    'status' => $center->status,
                    'latitud' => $center->latitude,
                    'longitud' => $center->longitude,
                ];
            });
    }

    /**
     * Exportar a CSV
     */
    private function exportToCsv($data, $filepath)
    {
        $file = fopen($filepath, 'w');
        
        // Escribir encabezados
        if ($data->isNotEmpty()) {
            fputcsv($file, array_keys($data->first()));
            
            // Escribir datos
            foreach ($data as $row) {
                fputcsv($file, $row);
            }
        }
        
        fclose($file);
    }

    /**
     * Exportar a JSON
     */
    private function exportToJson($data, $filepath)
    {
        $jsonData = [
            'metadata' => [
                'total_centers' => $data->count(),
                'export_date' => now()->toISOString(),
                'source' => 'Sistema de Gestión Electoral'
            ],
            'centers' => $data->toArray()
        ];
        
        file_put_contents($filepath, json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}

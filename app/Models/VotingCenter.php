<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class VotingCenter extends Model
{
    use HasFactory;

    protected $table = 'voting_centers';

    protected $fillable = [
        'code',
        'old_code',
        'name',
        'address',
        'state_id',
        'municipality_id',
        'parish_id',
        'total_voters',
        'total_tables',
        'status',
        'latitude',
        'longitude',
        'additional_info',
        'active', // Legacy compatibility
    ];

    protected $casts = [
        'additional_info' => 'array',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'active' => 'boolean', // Legacy compatibility
    ];

    /**
     * Relación con el estado
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Relación con el municipio
     */
    public function municipality(): BelongsTo
    {
        return $this->belongsTo(Municipality::class);
    }

    /**
     * Relación con la parroquia
     */
    public function parish(): BelongsTo
    {
        return $this->belongsTo(Parish::class, 'parish_id');
    }

    /**
     * Relación con las personas
     */
    public function people(): HasMany
    {
        return $this->hasMany(Person::class, 'voting_center_id');
    }

    /**
     * Relación con las mesas electorales
     */
    public function votingTables(): HasMany
    {
        return $this->hasMany(VotingTable::class);
    }

    /**
     * Scope para centros activos
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')->orWhere('active', true);
    }

    /**
     * Scope para buscar por estado
     */
    public function scopeByState($query, $stateId)
    {
        return $query->where('state_id', $stateId);
    }

    /**
     * Scope para buscar por municipio
     */
    public function scopeByMunicipality($query, $municipalityId)
    {
        return $query->where('municipality_id', $municipalityId);
    }

    /**
     * Scope para buscar por parroquia
     */
    public function scopeByParish($query, $parishId)
    {
        return $query->where('parish_id', $parishId);
    }

    /**
     * Obtener el nombre completo de la ubicación
     */
    public function getFullLocationAttribute(): string
    {
        return "{$this->state?->name}, {$this->municipality?->name}, {$this->parish?->name}";
    }

    /**
     * Obtener información resumida del centro
     */
    public function getSummaryAttribute(): string
    {
        return "{$this->name} - {$this->code} ({$this->total_voters} electores, {$this->total_tables} mesas)";
    }

    /**
     * Verificar si el centro tiene coordenadas GPS
     */
    public function hasCoordinates(): bool
    {
        return !is_null($this->latitude) && !is_null($this->longitude);
    }

    /**
     * Obtener centros cercanos (requiere coordenadas)
     */
    public function getNearbycenters($radiusKm = 5)
    {
        if (!$this->hasCoordinates()) {
            return collect();
        }

        // Fórmula de Haversine para calcular distancia
        return static::selectRaw("
            *,
            (6371 * acos(
                cos(radians(?)) * cos(radians(latitude)) *
                cos(radians(longitude) - radians(?)) +
                sin(radians(?)) * sin(radians(latitude))
            )) AS distance
        ", [$this->latitude, $this->longitude, $this->latitude])
        ->where('id', '!=', $this->id)
        ->whereNotNull('latitude')
        ->whereNotNull('longitude')
        ->having('distance', '<', $radiusKm)
        ->orderBy('distance')
        ->get();
    }

    // Legacy compatibility methods
    public function parroquia(): BelongsTo
    {
        return $this->parish();
    }

    public function personas(): HasMany
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}

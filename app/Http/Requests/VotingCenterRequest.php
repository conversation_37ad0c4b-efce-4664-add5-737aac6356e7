<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Municipality;
use App\Models\Parish;

class VotingCenterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $votingCenterId = $this->route('votingCenter')?->id;
        
        return [
            'code' => [
                'required',
                'string',
                'max:20',
                'regex:/^[0-9]{8,12}$/', // Solo números, 8-12 dígitos
                Rule::unique('voting_centers')->ignore($votingCenterId),
            ],
            'old_code' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[0-9A-Z\-]*$/', // Números, letras mayúsculas y guiones
            ],
            'name' => [
                'required',
                'string',
                'max:255',
                'min:5',
            ],
            'address' => [
                'nullable',
                'string',
                'max:500',
            ],
            'state_id' => [
                'required',
                'exists:states,id',
            ],
            'municipality_id' => [
                'required',
                'exists:municipalities,id',
                function ($attribute, $value, $fail) {
                    if ($this->filled('state_id')) {
                        $municipality = Municipality::where('id', $value)
                            ->where('state_id', $this->input('state_id'))
                            ->first();
                            
                        if (!$municipality) {
                            $fail('El municipio seleccionado no pertenece al estado especificado.');
                        }
                    }
                },
            ],
            'parish_id' => [
                'required',
                'exists:parishes,id',
                function ($attribute, $value, $fail) {
                    if ($this->filled('municipality_id')) {
                        $parish = Parish::where('id', $value)
                            ->where('municipality_id', $this->input('municipality_id'))
                            ->first();
                            
                        if (!$parish) {
                            $fail('La parroquia seleccionada no pertenece al municipio especificado.');
                        }
                    }
                },
            ],
            'total_voters' => [
                'nullable',
                'integer',
                'min:0',
                'max:50000', // Máximo realista para un centro
            ],
            'total_tables' => [
                'nullable',
                'integer',
                'min:0',
                'max:200', // Máximo realista para un centro
                function ($attribute, $value, $fail) {
                    $totalVoters = $this->input('total_voters', 0);
                    if ($totalVoters > 0 && $value > 0) {
                        $votersPerTable = $totalVoters / $value;
                        if ($votersPerTable > 600) {
                            $fail('El número de mesas es insuficiente para la cantidad de votantes (máximo 600 votantes por mesa).');
                        }
                        if ($votersPerTable < 50) {
                            $fail('El número de mesas es excesivo para la cantidad de votantes (mínimo 50 votantes por mesa).');
                        }
                    }
                },
            ],
            'status' => [
                'nullable',
                Rule::in(['active', 'inactive', 'suspended']),
            ],
            'latitude' => [
                'nullable',
                'numeric',
                'between:-90,90',
                function ($attribute, $value, $fail) {
                    // Validar que las coordenadas estén dentro de Venezuela
                    if ($value !== null && $this->filled('longitude')) {
                        $lng = $this->input('longitude');
                        if (!$this->isWithinVenezuela($value, $lng)) {
                            $fail('Las coordenadas especificadas no están dentro del territorio venezolano.');
                        }
                    }
                },
            ],
            'longitude' => [
                'nullable',
                'numeric',
                'between:-180,180',
            ],
            'additional_info' => [
                'nullable',
                'json',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'code.required' => 'El código del centro de votación es obligatorio.',
            'code.regex' => 'El código debe contener solo números y tener entre 8 y 12 dígitos.',
            'code.unique' => 'Ya existe un centro de votación con este código.',
            'name.required' => 'El nombre del centro de votación es obligatorio.',
            'name.min' => 'El nombre debe tener al menos 5 caracteres.',
            'state_id.required' => 'Debe seleccionar un estado.',
            'state_id.exists' => 'El estado seleccionado no es válido.',
            'municipality_id.required' => 'Debe seleccionar un municipio.',
            'municipality_id.exists' => 'El municipio seleccionado no es válido.',
            'parish_id.required' => 'Debe seleccionar una parroquia.',
            'parish_id.exists' => 'La parroquia seleccionada no es válida.',
            'total_voters.integer' => 'El total de votantes debe ser un número entero.',
            'total_voters.min' => 'El total de votantes no puede ser negativo.',
            'total_voters.max' => 'El total de votantes no puede exceder 50,000.',
            'total_tables.integer' => 'El total de mesas debe ser un número entero.',
            'total_tables.min' => 'El total de mesas no puede ser negativo.',
            'total_tables.max' => 'El total de mesas no puede exceder 200.',
            'latitude.between' => 'La latitud debe estar entre -90 y 90 grados.',
            'longitude.between' => 'La longitud debe estar entre -180 y 180 grados.',
            'additional_info.json' => 'La información adicional debe ser un JSON válido.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'code' => 'código',
            'old_code' => 'código anterior',
            'name' => 'nombre',
            'address' => 'dirección',
            'state_id' => 'estado',
            'municipality_id' => 'municipio',
            'parish_id' => 'parroquia',
            'total_voters' => 'total de votantes',
            'total_tables' => 'total de mesas',
            'status' => 'estado',
            'latitude' => 'latitud',
            'longitude' => 'longitud',
            'additional_info' => 'información adicional',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Limpiar y formatear el código
        if ($this->filled('code')) {
            $this->merge([
                'code' => preg_replace('/[^0-9]/', '', $this->input('code'))
            ]);
        }

        // Limpiar el nombre
        if ($this->filled('name')) {
            $this->merge([
                'name' => trim(preg_replace('/\s+/', ' ', $this->input('name')))
            ]);
        }

        // Calcular mesas automáticamente si no se proporciona
        if ($this->filled('total_voters') && !$this->filled('total_tables')) {
            $totalVoters = (int) $this->input('total_voters');
            $calculatedTables = max(1, intval($totalVoters / 400)); // 400 votantes por mesa
            $this->merge(['total_tables' => $calculatedTables]);
        }
    }

    /**
     * Verificar si las coordenadas están dentro de Venezuela
     */
    private function isWithinVenezuela(float $lat, float $lng): bool
    {
        // Límites aproximados de Venezuela
        $venezuelaBounds = [
            'north' => 15.8,   // Punto más al norte
            'south' => 0.6,    // Punto más al sur
            'east' => -59.8,   // Punto más al este
            'west' => -73.4,   // Punto más al oeste
        ];

        return $lat >= $venezuelaBounds['south'] && 
               $lat <= $venezuelaBounds['north'] && 
               $lng >= $venezuelaBounds['west'] && 
               $lng <= $venezuelaBounds['east'];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        $response = response()->json([
            'success' => false,
            'message' => 'Error de validación',
            'errors' => $validator->errors(),
            'suggestions' => $this->getValidationSuggestions($validator->errors())
        ], 422);

        throw new \Illuminate\Validation\ValidationException($validator, $response);
    }

    /**
     * Obtener sugerencias para errores de validación
     */
    private function getValidationSuggestions($errors): array
    {
        $suggestions = [];

        if ($errors->has('code')) {
            $suggestions['code'] = 'El código debe seguir el formato: EEMMPPPCCC (Estado-Municipio-Parroquia-Centro)';
        }

        if ($errors->has('total_tables') || $errors->has('total_voters')) {
            $suggestions['tables_voters'] = 'Recomendación: 300-500 votantes por mesa electoral';
        }

        if ($errors->has('latitude') || $errors->has('longitude')) {
            $suggestions['coordinates'] = 'Use herramientas como Google Maps para obtener coordenadas precisas';
        }

        return $suggestions;
    }
}

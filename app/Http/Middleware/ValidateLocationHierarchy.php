<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

class ValidateLocationHierarchy
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Solo validar en requests que contengan datos de ubicación
        if ($this->shouldValidate($request)) {
            $validation = $this->validateLocationHierarchy($request);
            
            if (!$validation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error de validación de ubicación',
                    'errors' => $validation['errors']
                ], 422);
            }
        }

        return $next($request);
    }

    /**
     * Determinar si se debe validar la jerarquía de ubicación
     */
    private function shouldValidate(Request $request): bool
    {
        return $request->hasAny(['state_id', 'municipality_id', 'parish_id']) &&
               in_array($request->method(), ['POST', 'PUT', 'PATCH']);
    }

    /**
     * Validar la jerarquía de ubicación
     */
    private function validateLocationHierarchy(Request $request): array
    {
        $errors = [];
        $stateId = $request->input('state_id');
        $municipalityId = $request->input('municipality_id');
        $parishId = $request->input('parish_id');

        // Validar que el estado existe
        if ($stateId) {
            $state = State::find($stateId);
            if (!$state) {
                $errors['state_id'] = 'El estado especificado no existe';
                return ['valid' => false, 'errors' => $errors];
            }

            // Validar que el municipio pertenece al estado
            if ($municipalityId) {
                $municipality = Municipality::where('id', $municipalityId)
                    ->where('state_id', $stateId)
                    ->first();
                    
                if (!$municipality) {
                    $errors['municipality_id'] = 'El municipio no pertenece al estado especificado';
                    return ['valid' => false, 'errors' => $errors];
                }

                // Validar que la parroquia pertenece al municipio
                if ($parishId) {
                    $parish = Parish::where('id', $parishId)
                        ->where('municipality_id', $municipalityId)
                        ->first();
                        
                    if (!$parish) {
                        $errors['parish_id'] = 'La parroquia no pertenece al municipio especificado';
                        return ['valid' => false, 'errors' => $errors];
                    }
                }
            }
        }

        return ['valid' => true, 'errors' => []];
    }
}

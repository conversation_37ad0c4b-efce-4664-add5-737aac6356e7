<?php

namespace App\Http\Controllers;

use App\Models\VotingCenter;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Requests\VotingCenterRequest;

class VotingCenterController extends Controller
{
    /**
     * Display a listing of voting centers
     */
    public function index(Request $request): JsonResponse
    {
        $query = VotingCenter::with(['state', 'municipality', 'parish']);

        // Filtros
        if ($request->has('state_id')) {
            $query->where('state_id', $request->state_id);
        }

        if ($request->has('municipality_id')) {
            $query->where('municipality_id', $request->municipality_id);
        }

        if ($request->has('parish_id')) {
            $query->where('parish_id', $request->parish_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%");
            });
        }

        // Ordenamiento
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginación
        $perPage = $request->get('per_page', 15);
        $centers = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $centers,
            'message' => 'Centros de votación obtenidos exitosamente'
        ]);
    }

    /**
     * Store a newly created voting center
     */
    public function store(VotingCenterRequest $request): JsonResponse
    {
        $center = VotingCenter::create($request->validated());

        return response()->json([
            'success' => true,
            'data' => $center->load(['state', 'municipality', 'parish']),
            'message' => 'Centro de votación creado exitosamente'
        ], 201);
    }

    /**
     * Display the specified voting center
     */
    public function show(VotingCenter $votingCenter): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $votingCenter->load(['state', 'municipality', 'parish']),
            'message' => 'Centro de votación obtenido exitosamente'
        ]);
    }

    /**
     * Update the specified voting center
     */
    public function update(VotingCenterRequest $request, VotingCenter $votingCenter): JsonResponse
    {
        $votingCenter->update($request->validated());

        return response()->json([
            'success' => true,
            'data' => $votingCenter->load(['state', 'municipality', 'parish']),
            'message' => 'Centro de votación actualizado exitosamente'
        ]);
    }

    /**
     * Remove the specified voting center
     */
    public function destroy(VotingCenter $votingCenter): JsonResponse
    {
        $votingCenter->delete();

        return response()->json([
            'success' => true,
            'message' => 'Centro de votación eliminado exitosamente'
        ]);
    }

    /**
     * Get voting centers by state
     */
    public function byState(State $state): JsonResponse
    {
        $centers = VotingCenter::where('state_id', $state->id)
            ->with(['municipality', 'parish'])
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $centers,
            'message' => "Centros de votación del estado {$state->name}"
        ]);
    }

    /**
     * Get voting centers by municipality
     */
    public function byMunicipality(Municipality $municipality): JsonResponse
    {
        $centers = VotingCenter::where('municipality_id', $municipality->id)
            ->with(['state', 'parish'])
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $centers,
            'message' => "Centros de votación del municipio {$municipality->name}"
        ]);
    }

    /**
     * Get voting centers by parish
     */
    public function byParish(Parish $parish): JsonResponse
    {
        $centers = VotingCenter::where('parish_id', $parish->id)
            ->with(['state', 'municipality'])
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $centers,
            'message' => "Centros de votación de la parroquia {$parish->name}"
        ]);
    }

    /**
     * Search voting centers by code
     */
    public function searchByCode(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string'
        ]);

        $center = VotingCenter::where('code', $request->code)
            ->with(['state', 'municipality', 'parish'])
            ->first();

        if (!$center) {
            return response()->json([
                'success' => false,
                'message' => 'Centro de votación no encontrado'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $center,
            'message' => 'Centro de votación encontrado'
        ]);
    }

    /**
     * Get voting centers statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_centers' => VotingCenter::count(),
            'active_centers' => VotingCenter::where('status', 'active')->count(),
            'inactive_centers' => VotingCenter::where('status', 'inactive')->count(),
            'suspended_centers' => VotingCenter::where('status', 'suspended')->count(),
            'total_voters' => VotingCenter::sum('total_voters'),
            'total_tables' => VotingCenter::sum('total_tables'),
            'centers_by_state' => VotingCenter::join('states', 'voting_centers.state_id', '=', 'states.id')
                ->selectRaw('states.name as state_name, COUNT(*) as total')
                ->groupBy('states.id', 'states.name')
                ->orderBy('total', 'desc')
                ->get(),
            'centers_with_coordinates' => VotingCenter::whereNotNull('latitude')
                ->whereNotNull('longitude')
                ->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Estadísticas de centros de votación'
        ]);
    }

    /**
     * Advanced search with multiple filters
     */
    public function advancedSearch(Request $request): JsonResponse
    {
        $request->validate([
            'search' => 'nullable|string|max:255',
            'state_id' => 'nullable|exists:states,id',
            'municipality_id' => 'nullable|exists:municipalities,id',
            'parish_id' => 'nullable|exists:parishes,id',
            'status' => 'nullable|in:active,inactive,suspended',
            'min_voters' => 'nullable|integer|min:0',
            'max_voters' => 'nullable|integer|min:0',
            'min_tables' => 'nullable|integer|min:0',
            'max_tables' => 'nullable|integer|min:0',
            'has_coordinates' => 'nullable|boolean',
            'radius' => 'nullable|numeric|min:0',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);

        $query = VotingCenter::with(['state', 'municipality', 'parish']);

        // Búsqueda por texto
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%")
                  ->orWhereHas('state', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('municipality', function ($mq) use ($search) {
                      $mq->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('parish', function ($pq) use ($search) {
                      $pq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filtros de ubicación
        if ($request->filled('state_id')) {
            $query->where('state_id', $request->state_id);
        }

        if ($request->filled('municipality_id')) {
            $query->where('municipality_id', $request->municipality_id);
        }

        if ($request->filled('parish_id')) {
            $query->where('parish_id', $request->parish_id);
        }

        // Filtro de status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filtros de votantes
        if ($request->filled('min_voters')) {
            $query->where('total_voters', '>=', $request->min_voters);
        }

        if ($request->filled('max_voters')) {
            $query->where('total_voters', '<=', $request->max_voters);
        }

        // Filtros de mesas
        if ($request->filled('min_tables')) {
            $query->where('total_tables', '>=', $request->min_tables);
        }

        if ($request->filled('max_tables')) {
            $query->where('total_tables', '<=', $request->max_tables);
        }

        // Filtro de coordenadas
        if ($request->filled('has_coordinates')) {
            if ($request->has_coordinates) {
                $query->whereNotNull('latitude')->whereNotNull('longitude');
            } else {
                $query->where(function ($q) {
                    $q->whereNull('latitude')->orWhereNull('longitude');
                });
            }
        }

        // Búsqueda por proximidad geográfica
        if ($request->filled(['latitude', 'longitude', 'radius'])) {
            $lat = $request->latitude;
            $lng = $request->longitude;
            $radius = $request->radius; // en kilómetros

            $query->whereRaw(
                "(6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?",
                [$lat, $lng, $lat, $radius]
            );
        }

        // Ordenamiento
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        $allowedSorts = ['name', 'code', 'total_voters', 'total_tables', 'created_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Paginación
        $perPage = min($request->get('per_page', 15), 100); // Máximo 100 por página
        $centers = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $centers,
            'filters_applied' => $request->only([
                'search', 'state_id', 'municipality_id', 'parish_id', 'status',
                'min_voters', 'max_voters', 'min_tables', 'max_tables',
                'has_coordinates', 'radius', 'latitude', 'longitude'
            ]),
            'message' => 'Búsqueda avanzada completada'
        ]);
    }

    /**
     * Get nearby voting centers
     */
    public function nearby(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:0.1|max:50', // máximo 50km
            'limit' => 'nullable|integer|min:1|max:50'
        ]);

        $lat = $request->latitude;
        $lng = $request->longitude;
        $radius = $request->get('radius', 5); // 5km por defecto
        $limit = $request->get('limit', 10);

        $centers = VotingCenter::with(['state', 'municipality', 'parish'])
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->selectRaw(
                "*, (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance",
                [$lat, $lng, $lat]
            )
            ->having('distance', '<=', $radius)
            ->orderBy('distance')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $centers,
            'search_params' => [
                'latitude' => $lat,
                'longitude' => $lng,
                'radius_km' => $radius,
                'limit' => $limit
            ],
            'message' => "Centros de votación encontrados en un radio de {$radius}km"
        ]);
    }

    /**
     * Export voting centers data
     */
    public function export(Request $request): JsonResponse
    {
        $format = $request->get('format', 'json');
        $filters = $request->only(['state_id', 'municipality_id', 'parish_id', 'status']);

        $query = VotingCenter::with(['state', 'municipality', 'parish']);

        // Aplicar filtros
        foreach ($filters as $key => $value) {
            if (!empty($value)) {
                $query->where($key, $value);
            }
        }

        $centers = $query->get();

        switch ($format) {
            case 'csv':
                return $this->exportToCsv($centers);

            case 'excel':
                return response()->json([
                    'success' => false,
                    'message' => 'Exportación Excel en desarrollo'
                ]);

            default:
                return response()->json([
                    'success' => true,
                    'data' => $centers,
                    'total' => $centers->count(),
                    'message' => 'Datos de centros de votación exportados'
                ]);
        }
    }

    /**
     * Export to CSV format
     */
    private function exportToCsv($centers)
    {
        $filename = 'centros_votacion_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($centers) {
            $file = fopen('php://output', 'w');

            // Encabezados CSV
            fputcsv($file, [
                'Código',
                'Nombre',
                'Dirección',
                'Estado',
                'Municipio',
                'Parroquia',
                'Total Votantes',
                'Total Mesas',
                'Status',
                'Latitud',
                'Longitud'
            ]);

            // Datos
            foreach ($centers as $center) {
                fputcsv($file, [
                    $center->code,
                    $center->name,
                    $center->address,
                    $center->state->name,
                    $center->municipality->name,
                    $center->parish->name,
                    $center->total_voters,
                    $center->total_tables,
                    $center->status,
                    $center->latitude,
                    $center->longitude
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Bulk update voting centers
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $request->validate([
            'centers' => 'required|array',
            'centers.*.id' => 'required|exists:voting_centers,id',
            'centers.*.status' => 'nullable|in:active,inactive,suspended',
            'centers.*.total_voters' => 'nullable|integer|min:0',
            'centers.*.total_tables' => 'nullable|integer|min:0',
        ]);

        $updated = 0;
        $errors = [];

        foreach ($request->centers as $centerData) {
            try {
                $center = VotingCenter::find($centerData['id']);
                $center->update(array_filter($centerData, function($value) {
                    return $value !== null;
                }));
                $updated++;
            } catch (\Exception $e) {
                $errors[] = [
                    'id' => $centerData['id'],
                    'error' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => true,
            'updated' => $updated,
            'errors' => $errors,
            'message' => "Se actualizaron {$updated} centros de votación"
        ]);
    }
}

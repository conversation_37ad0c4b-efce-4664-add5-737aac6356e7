<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\VotingCenter;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

class ReportsController extends Controller
{
    /**
     * Generate comprehensive electoral report
     */
    public function electoralReport(Request $request): JsonResponse
    {
        $request->validate([
            'state_id' => 'nullable|exists:states,id',
            'municipality_id' => 'nullable|exists:municipalities,id',
            'parish_id' => 'nullable|exists:parishes,id',
            'format' => 'nullable|in:json,csv,pdf',
            'include_details' => 'nullable|boolean',
        ]);

        $cacheKey = 'electoral_report_' . md5(serialize($request->only(['state_id', 'municipality_id', 'parish_id'])));
        
        $report = Cache::remember($cacheKey, 600, function () use ($request) {
            return $this->generateElectoralReport($request);
        });

        $format = $request->get('format', 'json');
        
        switch ($format) {
            case 'csv':
                return $this->exportReportToCsv($report);
            case 'pdf':
                return $this->exportReportToPdf($report);
            default:
                return response()->json([
                    'success' => true,
                    'data' => $report,
                    'generated_at' => now(),
                    'message' => 'Reporte electoral generado exitosamente'
                ]);
        }
    }

    /**
     * Generate voting centers distribution report
     */
    public function distributionReport(Request $request): JsonResponse
    {
        $request->validate([
            'group_by' => 'nullable|in:state,municipality,parish',
            'include_coordinates' => 'nullable|boolean',
            'min_voters' => 'nullable|integer|min:0',
            'max_voters' => 'nullable|integer|min:0',
        ]);

        $groupBy = $request->get('group_by', 'state');
        $includeCoordinates = $request->get('include_coordinates', false);
        
        $query = VotingCenter::with(['state', 'municipality', 'parish']);
        
        // Aplicar filtros
        if ($request->filled('min_voters')) {
            $query->where('total_voters', '>=', $request->min_voters);
        }
        
        if ($request->filled('max_voters')) {
            $query->where('total_voters', '<=', $request->max_voters);
        }
        
        if ($includeCoordinates) {
            $query->whereNotNull('latitude')->whereNotNull('longitude');
        }

        // Agrupar datos según el parámetro
        $distribution = $this->getDistributionData($query, $groupBy);
        
        return response()->json([
            'success' => true,
            'data' => [
                'distribution' => $distribution,
                'summary' => $this->getDistributionSummary($distribution),
                'filters_applied' => $request->only(['group_by', 'include_coordinates', 'min_voters', 'max_voters'])
            ],
            'message' => 'Reporte de distribución generado exitosamente'
        ]);
    }

    /**
     * Generate capacity analysis report
     */
    public function capacityReport(Request $request): JsonResponse
    {
        $request->validate([
            'state_id' => 'nullable|exists:states,id',
            'threshold_voters_per_table' => 'nullable|integer|min:100|max:800',
        ]);

        $threshold = $request->get('threshold_voters_per_table', 400);
        
        $query = VotingCenter::with(['state', 'municipality', 'parish'])
            ->where('total_voters', '>', 0)
            ->where('total_tables', '>', 0);
            
        if ($request->filled('state_id')) {
            $query->where('state_id', $request->state_id);
        }

        $centers = $query->get();
        
        $analysis = [
            'overcrowded' => [],
            'undercapacity' => [],
            'optimal' => [],
            'statistics' => [
                'total_centers' => $centers->count(),
                'total_voters' => $centers->sum('total_voters'),
                'total_tables' => $centers->sum('total_tables'),
                'average_voters_per_table' => 0,
            ]
        ];

        foreach ($centers as $center) {
            $votersPerTable = $center->total_tables > 0 ? $center->total_voters / $center->total_tables : 0;
            
            $centerData = [
                'id' => $center->id,
                'code' => $center->code,
                'name' => $center->name,
                'state' => $center->state->name,
                'municipality' => $center->municipality->name,
                'parish' => $center->parish->name,
                'total_voters' => $center->total_voters,
                'total_tables' => $center->total_tables,
                'voters_per_table' => round($votersPerTable, 1),
            ];

            if ($votersPerTable > $threshold) {
                $centerData['recommended_tables'] = ceil($center->total_voters / $threshold);
                $analysis['overcrowded'][] = $centerData;
            } elseif ($votersPerTable < ($threshold * 0.5)) {
                $centerData['recommended_tables'] = max(1, floor($center->total_voters / $threshold));
                $analysis['undercapacity'][] = $centerData;
            } else {
                $analysis['optimal'][] = $centerData;
            }
        }

        // Calcular estadísticas
        $totalTables = $centers->sum('total_tables');
        $analysis['statistics']['average_voters_per_table'] = $totalTables > 0 ? 
            round($centers->sum('total_voters') / $totalTables, 1) : 0;

        // Ordenar por severidad
        usort($analysis['overcrowded'], function($a, $b) {
            return $b['voters_per_table'] <=> $a['voters_per_table'];
        });

        usort($analysis['undercapacity'], function($a, $b) {
            return $a['voters_per_table'] <=> $b['voters_per_table'];
        });

        return response()->json([
            'success' => true,
            'data' => $analysis,
            'parameters' => [
                'threshold_voters_per_table' => $threshold,
                'state_id' => $request->state_id,
            ],
            'message' => 'Análisis de capacidad completado'
        ]);
    }

    /**
     * Generate geographic coverage report
     */
    public function geographicReport(Request $request): JsonResponse
    {
        $request->validate([
            'include_missing_coordinates' => 'nullable|boolean',
            'calculate_distances' => 'nullable|boolean',
        ]);

        $includeMissing = $request->get('include_missing_coordinates', true);
        $calculateDistances = $request->get('calculate_distances', false);

        // Centros con coordenadas
        $centersWithCoords = VotingCenter::with(['state', 'municipality', 'parish'])
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->get();

        // Centros sin coordenadas
        $centersWithoutCoords = VotingCenter::with(['state', 'municipality', 'parish'])
            ->where(function ($q) {
                $q->whereNull('latitude')->orWhereNull('longitude');
            })
            ->get();

        $report = [
            'coverage_summary' => [
                'total_centers' => VotingCenter::count(),
                'with_coordinates' => $centersWithCoords->count(),
                'without_coordinates' => $centersWithoutCoords->count(),
                'coverage_percentage' => VotingCenter::count() > 0 ? 
                    round(($centersWithCoords->count() / VotingCenter::count()) * 100, 1) : 0,
            ],
            'by_state' => $this->getGeographicCoverageByState(),
        ];

        if ($includeMissing) {
            $report['centers_without_coordinates'] = $centersWithoutCoords->map(function ($center) {
                return [
                    'id' => $center->id,
                    'code' => $center->code,
                    'name' => $center->name,
                    'state' => $center->state->name,
                    'municipality' => $center->municipality->name,
                    'parish' => $center->parish->name,
                ];
            });
        }

        if ($calculateDistances && $centersWithCoords->count() > 1) {
            $report['distance_analysis'] = $this->calculateDistanceAnalysis($centersWithCoords);
        }

        return response()->json([
            'success' => true,
            'data' => $report,
            'message' => 'Reporte geográfico generado exitosamente'
        ]);
    }

    /**
     * Generate electoral report data
     */
    private function generateElectoralReport(Request $request): array
    {
        $query = VotingCenter::with(['state', 'municipality', 'parish']);
        
        // Aplicar filtros
        if ($request->filled('state_id')) {
            $query->where('state_id', $request->state_id);
        }
        
        if ($request->filled('municipality_id')) {
            $query->where('municipality_id', $request->municipality_id);
        }
        
        if ($request->filled('parish_id')) {
            $query->where('parish_id', $request->parish_id);
        }

        $centers = $query->get();
        
        return [
            'summary' => [
                'total_centers' => $centers->count(),
                'active_centers' => $centers->where('status', 'active')->count(),
                'total_voters' => $centers->sum('total_voters'),
                'total_tables' => $centers->sum('total_tables'),
                'average_voters_per_center' => $centers->count() > 0 ? 
                    round($centers->avg('total_voters'), 0) : 0,
                'average_tables_per_center' => $centers->count() > 0 ? 
                    round($centers->avg('total_tables'), 1) : 0,
            ],
            'by_location' => $this->groupCentersByLocation($centers),
            'by_size' => $this->groupCentersBySize($centers),
            'status_distribution' => $centers->groupBy('status')->map->count(),
            'details' => $request->get('include_details', false) ? $centers : null,
        ];
    }

    /**
     * Get distribution data grouped by specified field
     */
    private function getDistributionData($query, string $groupBy): array
    {
        switch ($groupBy) {
            case 'municipality':
                return $query->join('municipalities', 'voting_centers.municipality_id', '=', 'municipalities.id')
                    ->join('states', 'municipalities.state_id', '=', 'states.id')
                    ->select('municipalities.name as municipality_name', 'states.name as state_name')
                    ->selectRaw('COUNT(*) as total_centers')
                    ->selectRaw('SUM(total_voters) as total_voters')
                    ->selectRaw('SUM(total_tables) as total_tables')
                    ->groupBy('municipalities.id', 'municipalities.name', 'states.name')
                    ->orderBy('total_centers', 'desc')
                    ->get()
                    ->toArray();
                    
            case 'parish':
                return $query->join('parishes', 'voting_centers.parish_id', '=', 'parishes.id')
                    ->join('municipalities', 'parishes.municipality_id', '=', 'municipalities.id')
                    ->join('states', 'municipalities.state_id', '=', 'states.id')
                    ->select('parishes.name as parish_name', 'municipalities.name as municipality_name', 'states.name as state_name')
                    ->selectRaw('COUNT(*) as total_centers')
                    ->selectRaw('SUM(total_voters) as total_voters')
                    ->selectRaw('SUM(total_tables) as total_tables')
                    ->groupBy('parishes.id', 'parishes.name', 'municipalities.name', 'states.name')
                    ->orderBy('total_centers', 'desc')
                    ->get()
                    ->toArray();
                    
            default: // state
                return $query->join('states', 'voting_centers.state_id', '=', 'states.id')
                    ->select('states.name as state_name')
                    ->selectRaw('COUNT(*) as total_centers')
                    ->selectRaw('SUM(total_voters) as total_voters')
                    ->selectRaw('SUM(total_tables) as total_tables')
                    ->groupBy('states.id', 'states.name')
                    ->orderBy('total_centers', 'desc')
                    ->get()
                    ->toArray();
        }
    }

    /**
     * Get distribution summary
     */
    private function getDistributionSummary(array $distribution): array
    {
        $totalCenters = array_sum(array_column($distribution, 'total_centers'));
        $totalVoters = array_sum(array_column($distribution, 'total_voters'));
        $totalTables = array_sum(array_column($distribution, 'total_tables'));
        
        return [
            'total_locations' => count($distribution),
            'total_centers' => $totalCenters,
            'total_voters' => $totalVoters,
            'total_tables' => $totalTables,
            'average_centers_per_location' => count($distribution) > 0 ? 
                round($totalCenters / count($distribution), 1) : 0,
        ];
    }

    /**
     * Group centers by location
     */
    private function groupCentersByLocation($centers): array
    {
        return $centers->groupBy('state.name')->map(function ($stateCenters, $stateName) {
            return [
                'state' => $stateName,
                'total_centers' => $stateCenters->count(),
                'total_voters' => $stateCenters->sum('total_voters'),
                'total_tables' => $stateCenters->sum('total_tables'),
                'municipalities' => $stateCenters->groupBy('municipality.name')->map(function ($munCenters, $munName) {
                    return [
                        'municipality' => $munName,
                        'total_centers' => $munCenters->count(),
                        'total_voters' => $munCenters->sum('total_voters'),
                        'total_tables' => $munCenters->sum('total_tables'),
                    ];
                })->values()->toArray(),
            ];
        })->values()->toArray();
    }

    /**
     * Group centers by size
     */
    private function groupCentersBySize($centers): array
    {
        return [
            'small' => ['range' => '< 500 votantes', 'count' => $centers->where('total_voters', '<', 500)->count()],
            'medium' => ['range' => '500-1500 votantes', 'count' => $centers->whereBetween('total_voters', [500, 1500])->count()],
            'large' => ['range' => '> 1500 votantes', 'count' => $centers->where('total_voters', '>', 1500)->count()],
        ];
    }

    /**
     * Get geographic coverage by state
     */
    private function getGeographicCoverageByState(): array
    {
        return DB::table('voting_centers')
            ->join('states', 'voting_centers.state_id', '=', 'states.id')
            ->select('states.name as state_name')
            ->selectRaw('COUNT(*) as total_centers')
            ->selectRaw('SUM(CASE WHEN latitude IS NOT NULL AND longitude IS NOT NULL THEN 1 ELSE 0 END) as with_coordinates')
            ->selectRaw('SUM(CASE WHEN latitude IS NULL OR longitude IS NULL THEN 1 ELSE 0 END) as without_coordinates')
            ->groupBy('states.id', 'states.name')
            ->orderBy('state_name')
            ->get()
            ->map(function ($item) {
                $item->coverage_percentage = $item->total_centers > 0 ? 
                    round(($item->with_coordinates / $item->total_centers) * 100, 1) : 0;
                return $item;
            })
            ->toArray();
    }

    /**
     * Calculate distance analysis between centers
     */
    private function calculateDistanceAnalysis($centers): array
    {
        $distances = [];
        $centersArray = $centers->toArray();
        
        for ($i = 0; $i < count($centersArray) - 1; $i++) {
            for ($j = $i + 1; $j < min($i + 10, count($centersArray)); $j++) { // Limitar para performance
                $distance = $this->calculateDistance(
                    $centersArray[$i]['latitude'], $centersArray[$i]['longitude'],
                    $centersArray[$j]['latitude'], $centersArray[$j]['longitude']
                );
                $distances[] = $distance;
            }
        }
        
        if (empty($distances)) {
            return ['message' => 'Insuficientes datos para análisis de distancia'];
        }
        
        sort($distances);
        
        return [
            'min_distance_km' => round(min($distances), 2),
            'max_distance_km' => round(max($distances), 2),
            'average_distance_km' => round(array_sum($distances) / count($distances), 2),
            'median_distance_km' => round($distances[intval(count($distances) / 2)], 2),
            'sample_size' => count($distances),
        ];
    }

    /**
     * Calculate distance between two points using Haversine formula
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2): float
    {
        $earthRadius = 6371; // km
        
        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);
        
        $a = sin($dLat/2) * sin($dLat/2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLng/2) * sin($dLng/2);
             
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        
        return $earthRadius * $c;
    }

    /**
     * Export report to CSV
     */
    private function exportReportToCsv($report)
    {
        $filename = 'reporte_electoral_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($report) {
            $file = fopen('php://output', 'w');
            
            // Escribir resumen
            fputcsv($file, ['RESUMEN ELECTORAL']);
            fputcsv($file, ['Total Centros', $report['summary']['total_centers']]);
            fputcsv($file, ['Centros Activos', $report['summary']['active_centers']]);
            fputcsv($file, ['Total Votantes', $report['summary']['total_voters']]);
            fputcsv($file, ['Total Mesas', $report['summary']['total_tables']]);
            fputcsv($file, []);
            
            // Escribir datos por ubicación si existen
            if (isset($report['by_location'])) {
                fputcsv($file, ['DISTRIBUCIÓN POR ESTADO']);
                fputcsv($file, ['Estado', 'Centros', 'Votantes', 'Mesas']);
                
                foreach ($report['by_location'] as $state) {
                    fputcsv($file, [
                        $state['state'],
                        $state['total_centers'],
                        $state['total_voters'],
                        $state['total_tables']
                    ]);
                }
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export report to PDF (placeholder)
     */
    private function exportReportToPdf($report)
    {
        return response()->json([
            'success' => false,
            'message' => 'Exportación a PDF en desarrollo',
            'data' => $report
        ]);
    }
}

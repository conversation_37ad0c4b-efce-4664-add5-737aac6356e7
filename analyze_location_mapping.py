import pandas as pd

def analyze_excel_data():
    """Analyze the Excel data to understand what locations we have"""
    print("🔍 Analizando datos del Excel para mapear ubicaciones...")
    
    # Read the Excel file
    df = pd.read_excel('database/seeders/data/centros.xlsx')
    df_clean = df.iloc[:, :13].copy()
    
    print(f"📊 Total records: {len(df_clean)}")
    
    # Analyze states
    print("\n🗺️ ESTADOS en el Excel:")
    states = df_clean['estado_corto'].value_counts()
    for state, count in states.items():
        print(f"   {state}: {count} centros")
    
    # Analyze municipalities by state
    print("\n🏛️ MUNICIPIOS por estado (primeros 5 de cada estado):")
    for state in df_clean['estado_corto'].unique():
        if pd.notna(state):
            state_data = df_clean[df_clean['estado_corto'] == state]
            municipalities = state_data['municipio_corto'].value_counts().head(5)
            print(f"\n   {state}:")
            for mun, count in municipalities.items():
                print(f"      {mun}: {count} centros")
    
    # Analyze parishes by municipality for Amazonas (as example)
    print("\n🏘️ PARROQUIAS de Amazonas (ejemplo):")
    amazonas_data = df_clean[df_clean['estado_corto'] == 'AMAZONAS']
    for municipality in amazonas_data['municipio_corto'].unique():
        if pd.notna(municipality):
            mun_data = amazonas_data[amazonas_data['municipio_corto'] == municipality]
            parishes = mun_data['parroquia_corto'].unique()
            print(f"\n   {municipality}:")
            for parish in parishes:
                if pd.notna(parish):
                    print(f"      {parish}")

def create_comprehensive_mapping():
    """Create comprehensive mapping based on UbicacionesSeeder data"""
    
    # States mapping (from UbicacionesSeeder)
    states_db = {
        'Amazonas': 'AM',
        'Anzoátegui': 'AN', 
        'Apure': 'AP',
        'Aragua': 'AR',
        'Barinas': 'BA',
        'Bolívar': 'BO',
        'Carabobo': 'CA',
        'Cojedes': 'CO',
        'Delta Amacuro': 'DA',
        'Distrito Capital': 'DC',
        'Falcón': 'FA',
        'Guárico': 'GU',
        'La Guaira': 'LG',
        'Lara': 'LA',
        'Mérida': 'ME',
        'Miranda': 'MI',
        'Monagas': 'MO',
        'Nueva Esparta': 'NE',
        'Portuguesa': 'PO',
        'Sucre': 'SU',
        'Táchira': 'TA',
        'Trujillo': 'TR',
        'Yaracuy': 'YA',
        'Zulia': 'ZU',
        'Dependencias Federales': 'DF'
    }
    
    # Excel to DB state mapping
    excel_to_db_states = {
        'AMAZONAS': 'Amazonas',
        'ANZOATEGUI': 'Anzoátegui',
        'APURE': 'Apure', 
        'ARAGUA': 'Aragua',
        'BARINAS': 'Barinas',
        'BOLIVAR': 'Bolívar',
        'CARABOBO': 'Carabobo',
        'COJEDES': 'Cojedes',
        'DELTA AMACURO': 'Delta Amacuro',
        'CAPITAL': 'Distrito Capital',
        'FALCON': 'Falcón',
        'GUARICO': 'Guárico',
        'LA GUAIRA': 'La Guaira',
        'LARA': 'Lara',
        'MERIDA': 'Mérida',
        'MIRANDA': 'Miranda',
        'MONAGAS': 'Monagas',
        'NUEVA ESPARTA': 'Nueva Esparta',
        'PORTUGUESA': 'Portuguesa',
        'SUCRE': 'Sucre',
        'TACHIRA': 'Táchira',
        'TRUJILLO': 'Trujillo',
        'YARACUY': 'Yaracuy',
        'ZULIA': 'Zulia',
        'DEPENDENCIAS FEDERALES': 'Dependencias Federales',
        'EXTERIOR': None  # Skip exterior records
    }
    
    # Amazonas municipalities (from UbicacionesSeeder)
    amazonas_municipalities_db = {
        'Alto Orinoco': 'ALO',
        'Atabapo': 'ATA', 
        'Atures': 'ATU',
        'Autana': 'AUT',
        'Manapiare': 'MAN',
        'Maroa': 'MAR',
        'Río Negro': 'RNE'
    }
    
    # Excel to DB municipality mapping for Amazonas
    excel_to_db_amazonas_municipalities = {
        'ALTO ORINOCO': 'Alto Orinoco',
        'ATABAPO': 'Atabapo',
        'ATURES': 'Atures', 
        'AUTANA': 'Autana',
        'MANAPIARE': 'Manapiare',
        'MAROA': 'Maroa',
        'RIO NEGRO': 'Río Negro'
    }
    
    # Amazonas parishes (from UbicacionesSeeder)
    amazonas_parishes_db = {
        # Alto Orinoco
        'La Esmeralda': 'LES',
        'Huachamacare': 'HUA', 
        'Marawaka': 'MAR',
        'Mavaca': 'MAV',
        'Sierra Parima': 'SPA',
        # Atabapo  
        'San Fernando de Atabapo': 'SFA',
        'Ucata': 'UCA',
        'Yapacana': 'YAP',
        'Caname': 'CAN',
        # Atures
        'Fernando Girón Tovar': 'FGT',
        'Luis Alberto Gómez': 'LAG',
        'Pahueña': 'PAH', 
        'Platanillal': 'PLA',
        # Autana
        'Isla de Ratón': 'IDR',
        'Samariapo': 'SAM',
        'Sipapo': 'SIP',
        'Munduapo': 'MUN',
        'Guayapo': 'GUA',
        # Manapiare
        'San Juan de Manapiare': 'SJM',
        'Alto Ventuari': 'AVE',
        'Medio Ventuari': 'MVE',
        'Bajo Ventuari': 'BVE',
        # Maroa
        'Maroa': 'MAR2',
        'Victorino': 'VIC',
        'Comunidad': 'COM',
        # Río Negro
        'San Carlos de Río Negro': 'SCR',
        'Solano': 'SOL',
        'Casiquiare': 'CAS',
        'Cocuy': 'COC'
    }
    
    return excel_to_db_states, excel_to_db_amazonas_municipalities, amazonas_parishes_db

if __name__ == "__main__":
    analyze_excel_data()
    print("\n" + "="*50)
    excel_to_db_states, excel_to_db_amazonas_municipalities, amazonas_parishes_db = create_comprehensive_mapping()
    
    print("\n📋 MAPEO DE ESTADOS:")
    for excel_name, db_name in excel_to_db_states.items():
        print(f"   '{excel_name}' -> '{db_name}'")
    
    print("\n📋 MAPEO DE MUNICIPIOS AMAZONAS:")
    for excel_name, db_name in excel_to_db_amazonas_municipalities.items():
        print(f"   '{excel_name}' -> '{db_name}'")

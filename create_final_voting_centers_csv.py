import pandas as pd
import math
import random

def create_final_csv():
    """Create final CSV with correct mappings"""
    print("🔧 Creando CSV final con mapeos correctos...")
    
    # Read the Excel file
    df = pd.read_excel('database/seeders/data/centros.xlsx')
    df_clean = df.iloc[:, :13].copy()
    
    print(f"📊 Total records in Excel: {len(df_clean)}")
    
    # Filter out EXTERIOR records
    df_clean = df_clean[df_clean['estado_corto'] != 'EXTERIOR']
    print(f"📊 Records after filtering EXTERIOR: {len(df_clean)}")
    
    # State mapping (Excel -> Database)
    state_mapping = {
        'AMAZONAS': 'Amazonas',
        'ANZOATEGUI': 'Anzoátegui',
        'APURE': 'Apure',
        'ARAGUA': 'Aragua',
        'BARINAS': 'Barinas',
        'BOLIVAR': 'Bolívar',
        'CARABOBO': 'Carabobo',
        'COJEDES': 'Cojedes',
        'DELTA AMACURO': 'Delta Amacuro',
        'CAPITAL': 'Distrito Capital',
        'FALCON': 'Falcón',
        'GUARICO': 'Guárico',
        'LA GUAIRA': 'La Guaira',
        'LARA': 'Lara',
        'MERIDA': 'Mérida',
        'MIRANDA': 'Miranda',
        'MONAGAS': 'Monagas',
        'NUEVA ESPARTA': 'Nueva Esparta',
        'PORTUGUESA': 'Portuguesa',
        'SUCRE': 'Sucre',
        'TACHIRA': 'Táchira',
        'TRUJILLO': 'Trujillo',
        'YARACUY': 'Yaracuy',
        'ZULIA': 'Zulia',
        'DEPENDENCIAS FEDERALES': 'Dependencias Federales'
    }
    
    # Municipality mappings (simplified - convert to title case and handle special cases)
    def map_municipality(excel_name, state_name):
        # Clean the name
        clean_name = str(excel_name).strip()
        
        # Remove prefixes
        prefixes = ['MP. ', 'MUNICIPIO ', 'MUN. ']
        for prefix in prefixes:
            if clean_name.upper().startswith(prefix):
                clean_name = clean_name[len(prefix):].strip()
        
        # Convert to title case
        clean_name = clean_name.title()
        
        # Handle special cases
        special_cases = {
            'Blvno Libertador': 'Libertador',
            'Rio Negro': 'Río Negro',
            'Jose Felix Ribas': 'José Félix Ribas',
            'Francisco Linares Alcantara': 'Francisco Linares Alcántara',
            'Santiago Mariño': 'Santiago Mariño',
            'Pao De San Juan Bautista': 'Pao de San Juan Bautista',
            'Romulo Gallegos': 'Rómulo Gallegos',
            'Antonio Diaz': 'Antonio Díaz',
            'Simon Rodriguez': 'Simón Rodríguez',
            'Simon Bolivar': 'Simón Bolívar',
            'Andres Eloy Blanco': 'Andrés Eloy Blanco',
            'Juan Jose Mora': 'Juan José Mora',
            'Carlos Arvelo': 'Carlos Arvelo',
            'Puerto Cabello': 'Puerto Cabello',
            'San Cristobal': 'San Cristóbal',
            'Bocono': 'Boconó',
            'Alberto Adriani': 'Alberto Adriani',
            'Tulio Febres Cordero': 'Tulio Febres Cordero',
            'Andres Mata': 'Andrés Mata',
            'Ezequiel Zamora': 'Ezequiel Zamora',
            'Pedro Camejo': 'Pedro Camejo',
            'Maturin': 'Maturín',
            'Mariño': 'Mariño',
            'Díaz': 'Díaz',
            'García': 'García',
            'Gómez': 'Gómez',
            'Turen': 'Turén',
            'Paez': 'Páez',
            'Bermudez': 'Bermúdez',
            'Ribero': 'Ribero',
            'Cardenas': 'Cárdenas',
            'Junin': 'Junín',
            'Peña': 'Peña',
            'Bruzual': 'Bruzual',
            'Bolivar': 'Bolívar'
        }
        
        return special_cases.get(clean_name, clean_name)
    
    # Parish mappings (simplified - convert to title case and handle special cases)
    def map_parish(excel_name, municipality_name, state_name):
        # Clean the name
        clean_name = str(excel_name).strip()
        
        # Remove prefixes
        prefixes = ['PQ. ', 'PARROQUIA ']
        for prefix in prefixes:
            if clean_name.upper().startswith(prefix):
                clean_name = clean_name[len(prefix):].strip()
        
        # Convert to title case
        clean_name = clean_name.title()
        
        # Handle special cases
        special_cases = {
            'Fernando Giron Tovar': 'Fernando Girón Tovar',
            'Luis Alberto Gomez': 'Luis Alberto Gómez',
            'Parhueña': 'Pahueña',
            'San Fernando De Atabapo': 'San Fernando de Atabapo',
            'San Carlos De Rio Negro': 'San Carlos de Río Negro',
            'Isla De Raton': 'Isla de Ratón',
            'San Juan De Manapiare': 'San Juan de Manapiare',
            'Jose Felix Ribas': 'José Félix Ribas',
            'Simon Bolivar': 'Simón Bolívar',
            'Simon Rodriguez': 'Simón Rodríguez',
            'Andres Eloy Blanco': 'Andrés Eloy Blanco',
            'Jose Maria Vargas': 'José María Vargas',
            'Antonio Jose De Sucre': 'Antonio José de Sucre',
            'San Jose': 'San José',
            'Jesus Maria Semprun': 'Jesús María Semprún',
            'Marcano': 'Marcano',
            'Tubores': 'Tubores',
            'Mariño': 'Mariño',
            'Díaz': 'Díaz',
            'García': 'García',
            'Gómez': 'Gómez',
            'Arismendi': 'Arismendi',
            'Maneiro': 'Maneiro',
            'Villalba': 'Villalba'
        }
        
        return special_cases.get(clean_name, clean_name)
    
    def calculate_tables(total_voters):
        """Calculate number of voting tables based on total voters"""
        if total_voters <= 0:
            return 1
        tables = math.ceil(total_voters / 450)
        return max(1, tables)
    
    def generate_random_coordinates():
        """Generate random coordinates within Venezuela's approximate bounds"""
        lat_min, lat_max = 0.5, 12.5
        lon_min, lon_max = -73.5, -59.5
        
        latitude = round(random.uniform(lat_min, lat_max), 3)
        longitude = round(random.uniform(lon_min, lon_max), 3)
        
        return latitude, longitude
    
    # Process data
    output_data = []
    skipped_count = 0
    
    for idx, row in df_clean.iterrows():
        try:
            # Map names
            excel_state = str(row['estado_corto']).strip()
            state_name = state_mapping.get(excel_state)
            
            if not state_name:
                print(f"⚠️  Estado no mapeado: {excel_state}")
                skipped_count += 1
                continue
            
            municipality_name = map_municipality(row['municipio_corto'], state_name)
            parish_name = map_parish(row['parroquia_corto'], municipality_name, state_name)
            
            # Calculate tables
            total_voters = int(row['electores_total']) if pd.notna(row['electores_total']) else 0
            total_tables = calculate_tables(total_voters)
            
            # Generate coordinates
            latitude, longitude = generate_random_coordinates()
            
            # Generate a simple sequential code
            code = f"VE{len(output_data)+1:06d}"
            
            # Create output record
            output_record = {
                'code': code,
                'old_code': '',
                'name': str(row['centro']).strip() if pd.notna(row['centro']) else f'Centro {code}',
                'address': f"Centro de Votación {row['centro']}, {parish_name}, {municipality_name}, {state_name}" if pd.notna(row['centro']) else '',
                'state_name': state_name,
                'municipality_name': municipality_name,
                'parish_name': parish_name,
                'total_voters': total_voters,
                'total_tables': total_tables,
                'status': 'active',
                'latitude': latitude,
                'longitude': longitude
            }
            
            output_data.append(output_record)
            
            if len(output_data) % 1000 == 0:
                print(f"✅ Processed {len(output_data)} centers...")
                
        except Exception as e:
            print(f"❌ Error processing row {idx}: {e}")
            skipped_count += 1
            continue
    
    # Create output DataFrame
    output_df = pd.DataFrame(output_data)
    
    # Sort by state, municipality, parish, and code
    output_df = output_df.sort_values(['state_name', 'municipality_name', 'parish_name', 'code'])
    
    # Save to CSV
    output_file = 'database/seeders/data/voting_centers_final.csv'
    output_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"\n🎉 CSV final creado exitosamente!")
    print(f"📄 Archivo: {output_file}")
    print(f"📊 Centros procesados: {len(output_df)}")
    print(f"⏭️  Registros omitidos: {skipped_count}")
    print(f"🗺️  Estados: {output_df['state_name'].nunique()}")
    print(f"🏛️  Municipios: {output_df['municipality_name'].nunique()}")
    print(f"🏘️  Parroquias: {output_df['parish_name'].nunique()}")
    print(f"👥 Total electores: {output_df['total_voters'].sum():,}")
    print(f"🗳️  Total mesas: {output_df['total_tables'].sum():,}")
    
    # Show sample data
    print(f"\n📋 Muestra de datos finales:")
    print(output_df[['code', 'name', 'state_name', 'municipality_name', 'parish_name', 'total_voters']].head(10))
    
    return output_file

if __name__ == "__main__":
    create_final_csv()

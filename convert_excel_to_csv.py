import pandas as pd
import math
import random

def map_state_name(excel_state):
    """Map Excel state names to database state names"""
    state_mapping = {
        'CAPITAL': 'Distrito Capital',
        'ANZOATEGUI': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        'APURE': '<PERSON>pure',
        'ARAGUA': 'Aragua',
        'BARINAS': '<PERSON>nas',
        'BOLIVAR': 'Bo<PERSON><PERSON><PERSON>',
        'CARABOBO': 'Carabobo',
        'COJEDES': 'Coje<PERSON>',
        'FALCON': '<PERSON>alcón',
        'GUARICO': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        'LARA': '<PERSON>',
        'MERIDA': '<PERSON><PERSON><PERSON>',
        'MIRANDA': '<PERSON>',
        'MONAGAS': 'Mona<PERSON>',
        'NUEVA ESPARTA': 'Nueva Esparta',
        'PORTUGUESA': 'Portuguesa',
        'SUCRE': 'Sucre',
        'TACHIRA': 'Tá<PERSON>ra',
        'TRUJILLO': '<PERSON>ru<PERSON><PERSON>',
        'YARACUY': '<PERSON><PERSON><PERSON>',
        'ZULIA': '<PERSON><PERSON><PERSON>',
        'AMAZONAS': 'Amazonas',
        'DELTA AMACURO': 'Delta Amacuro',
        'LA GUAIRA': 'La Guaira',
        'DEPENDENCIAS FEDERALES': 'Dependencias Federales'
    }
    return state_mapping.get(excel_state.upper(), excel_state)

def clean_municipality_name(name):
    """Clean municipality names"""
    if pd.isna(name):
        return name
    # Remove common prefixes
    name = str(name).strip()
    prefixes_to_remove = ['MP. ', 'MUNICIPIO ', 'MUN. ']
    for prefix in prefixes_to_remove:
        if name.upper().startswith(prefix):
            name = name[len(prefix):].strip()
    return name

def clean_parish_name(name):
    """Clean parish names"""
    if pd.isna(name):
        return name
    # Remove common prefixes
    name = str(name).strip()
    prefixes_to_remove = ['PQ. ', 'PARROQUIA ']
    for prefix in prefixes_to_remove:
        if name.upper().startswith(prefix):
            name = name[len(prefix):].strip()
    return name

def generate_voting_center_code(state_id, municipality_id, parish_id, center_number):
    """Generate a voting center code in format: SSMMPPNNN"""
    return f"{state_id:02d}{municipality_id:02d}{parish_id:02d}{center_number:03d}"

def calculate_tables(total_voters):
    """Calculate number of voting tables based on total voters"""
    if total_voters <= 0:
        return 1
    # Approximately 400-500 voters per table
    tables = math.ceil(total_voters / 450)
    return max(1, tables)

def generate_random_coordinates():
    """Generate random coordinates within Venezuela's approximate bounds"""
    # Venezuela approximate bounds
    lat_min, lat_max = 0.5, 12.5
    lon_min, lon_max = -73.5, -59.5
    
    latitude = round(random.uniform(lat_min, lat_max), 3)
    longitude = round(random.uniform(lon_min, lon_max), 3)
    
    return latitude, longitude

def main():
    print("🇻🇪 Converting Excel voting centers data to CSV format...")
    
    # Read the Excel file
    df = pd.read_excel('database/seeders/data/centros.xlsx')
    
    # Remove unnamed columns and clean data
    df_clean = df.iloc[:, :13].copy()
    
    print(f"📊 Total voting centers in Excel: {len(df_clean)}")
    
    # Clean and prepare data
    df_clean['state_name'] = df_clean['estado_corto'].apply(map_state_name)
    df_clean['municipality_name'] = df_clean['municipio_corto'].apply(clean_municipality_name)
    df_clean['parish_name'] = df_clean['parroquia_corto'].apply(clean_parish_name)
    
    # Create output DataFrame with required columns
    output_data = []
    
    # Group by state, municipality, parish to assign sequential IDs
    state_counter = {}
    municipality_counter = {}
    parish_counter = {}
    center_counter = {}
    
    for idx, row in df_clean.iterrows():
        state_name = row['state_name']
        municipality_name = row['municipality_name']
        parish_name = row['parish_name']
        
        # Assign state ID
        if state_name not in state_counter:
            state_counter[state_name] = len(state_counter) + 1
        state_id = state_counter[state_name]
        
        # Assign municipality ID
        mun_key = f"{state_name}|{municipality_name}"
        if mun_key not in municipality_counter:
            municipality_counter[mun_key] = len([k for k in municipality_counter.keys() if k.startswith(f"{state_name}|")]) + 1
        municipality_id = municipality_counter[mun_key]
        
        # Assign parish ID
        parish_key = f"{state_name}|{municipality_name}|{parish_name}"
        if parish_key not in parish_counter:
            parish_counter[parish_key] = len([k for k in parish_counter.keys() if k.startswith(f"{state_name}|{municipality_name}|")]) + 1
        parish_id = parish_counter[parish_key]
        
        # Assign center number within parish
        if parish_key not in center_counter:
            center_counter[parish_key] = 0
        center_counter[parish_key] += 1
        center_number = center_counter[parish_key]
        
        # Generate voting center code
        code = generate_voting_center_code(state_id, municipality_id, parish_id, center_number)
        
        # Calculate tables
        total_voters = int(row['electores_total']) if pd.notna(row['electores_total']) else 0
        total_tables = calculate_tables(total_voters)
        
        # Generate coordinates
        latitude, longitude = generate_random_coordinates()
        
        # Create output record
        output_record = {
            'code': code,
            'old_code': '',  # Empty for new data
            'name': str(row['centro']).strip() if pd.notna(row['centro']) else f'Centro {code}',
            'address': f"Centro de Votación {row['centro']}, {parish_name}, {municipality_name}, {state_name}" if pd.notna(row['centro']) else '',
            'state_name': state_name,
            'municipality_name': municipality_name,
            'parish_name': parish_name,
            'state_id': state_id,
            'municipality_id': municipality_id,
            'parish_id': parish_id,
            'total_voters': total_voters,
            'total_tables': total_tables,
            'status': 'active',
            'latitude': latitude,
            'longitude': longitude
        }
        
        output_data.append(output_record)
        
        if len(output_data) % 1000 == 0:
            print(f"✅ Processed {len(output_data)} centers...")
    
    # Create output DataFrame
    output_df = pd.DataFrame(output_data)
    
    # Sort by state, municipality, parish, and code
    output_df = output_df.sort_values(['state_name', 'municipality_name', 'parish_name', 'code'])
    
    # Save to CSV
    output_file = 'database/seeders/data/voting_centers_complete.csv'
    output_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"\n🎉 Conversion completed successfully!")
    print(f"📄 Output file: {output_file}")
    print(f"📊 Total centers processed: {len(output_df)}")
    print(f"🗺️  States: {output_df['state_id'].nunique()}")
    print(f"🏛️  Municipalities: {output_df['municipality_id'].nunique()}")
    print(f"🏘️  Parishes: {output_df['parish_id'].nunique()}")
    print(f"👥 Total voters: {output_df['total_voters'].sum():,}")
    print(f"🗳️  Total tables: {output_df['total_tables'].sum():,}")
    
    # Show sample data
    print(f"\n📋 Sample of converted data:")
    print(output_df[['code', 'name', 'state_name', 'municipality_name', 'parish_name', 'total_voters', 'total_tables']].head(10))

if __name__ == "__main__":
    main()

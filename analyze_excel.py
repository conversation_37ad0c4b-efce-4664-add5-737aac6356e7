import pandas as pd

# Read the Excel file
df = pd.read_excel('database/seeders/data/centros.xlsx')

# Remove unnamed columns (they seem to be empty)
df_clean = df.iloc[:, :13]  # Keep only the first 13 columns

print('Cleaned data structure:')
print(f'Shape: {df_clean.shape}')
print('\nColumns:')
for i, col in enumerate(df_clean.columns):
    print(f'{i+1}. {col}')

print('\nFirst 10 rows:')
print(df_clean.head(10))

print('\nSample data from different states:')
print(df_clean[['estado_corto', 'municipio_corto', 'parroquia_corto', 'centro', 'electores_total']].head(20))

print('\nUnique states:')
print(df_clean['estado_corto'].unique()[:10])  # Show first 10 states

print('\nData summary:')
print(f'Total voting centers: {len(df_clean)}')
print(f'Total states: {df_clean["estado_cne_id"].nunique()}')
print(f'Total municipalities: {df_clean["municipio_cne_id"].nunique()}')
print(f'Total parishes: {df_clean["parroquia_cne_id"].nunique()}')

<?php

/**
 * Script para obtener todos los centros de votación de Venezuela
 * Utiliza la API de vzlapi.com y datos públicos del CNE
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class VotingCentersFetcher
{
    private $baseUrl = 'https://api.vzlapi.com';
    private $outputFile = 'database/seeders/data/voting_centers.csv';
    private $centers = [];
    private $processedCenters = [];

    public function __construct()
    {
        echo "🇻🇪 Iniciando descarga de centros de votación de Venezuela...\n";
        $this->ensureDirectoryExists();
    }

    /**
     * Método principal para obtener todos los centros
     */
    public function fetchAllCenters()
    {
        echo "📊 Obteniendo datos de centros de votación...\n";
        
        // Estrategia: usar cédulas secuenciales para mapear todos los centros
        $this->fetchCentersByCedulaRange();
        
        // Generar CSV
        $this->generateCSV();
        
        echo "✅ Proceso completado. Total de centros únicos: " . count($this->processedCenters) . "\n";
        echo "📄 Archivo generado: {$this->outputFile}\n";
    }

    /**
     * Obtener centros usando rangos de cédulas
     */
    private function fetchCentersByCedulaRange()
    {
        echo "🔍 Mapeando centros por rangos de cédulas...\n";
        
        // Rangos de cédulas por estado (aproximados)
        $cedulaRanges = [
            // Distrito Capital
            ['start' => 1000000, 'end' => 2000000, 'state' => 'DTTO. CAPITAL'],
            // Miranda
            ['start' => 10000000, 'end' => 15000000, 'state' => 'EDO. MIRANDA'],
            // Zulia
            ['start' => 15000000, 'end' => 22000000, 'state' => 'EDO. ZULIA'],
            // Carabobo
            ['start' => 7000000, 'end' => 9000000, 'state' => 'EDO. CARABOBO'],
            // Lara
            ['start' => 11000000, 'end' => 12000000, 'state' => 'EDO. LARA'],
            // Aragua
            ['start' => 4000000, 'end' => 5000000, 'state' => 'EDO. ARAGUA'],
            // Anzoátegui
            ['start' => 2000000, 'end' => 3000000, 'state' => 'EDO. ANZOATEGUI'],
            // Táchira
            ['start' => 18000000, 'end' => 19000000, 'state' => 'EDO. TACHIRA'],
            // Bolívar
            ['start' => 6000000, 'end' => 7000000, 'state' => 'EDO. BOLIVAR'],
            // Mérida
            ['start' => 12000000, 'end' => 13000000, 'state' => 'EDO. MERIDA'],
        ];

        foreach ($cedulaRanges as $range) {
            echo "🔄 Procesando rango {$range['start']} - {$range['end']} ({$range['state']})...\n";
            $this->fetchCentersInRange($range['start'], $range['end'], 5000); // Saltos de 5000
        }
    }

    /**
     * Obtener centros en un rango específico
     */
    private function fetchCentersInRange($start, $end, $step = 1000)
    {
        for ($cedula = $start; $cedula <= $end; $cedula += $step) {
            try {
                $response = $this->makeApiRequest($cedula);
                
                if ($response && isset($response['CenterID'])) {
                    $this->processCenterData($response);
                }
                
                // Pausa para no sobrecargar la API
                usleep(100000); // 100ms
                
                if ($cedula % 50000 === 0) {
                    echo "   📍 Procesadas cédulas hasta: {$cedula} - Centros únicos: " . count($this->processedCenters) . "\n";
                }
                
            } catch (Exception $e) {
                echo "⚠️  Error con cédula {$cedula}: " . $e->getMessage() . "\n";
                continue;
            }
        }
    }

    /**
     * Realizar petición a la API
     */
    private function makeApiRequest($cedula)
    {
        $url = "{$this->baseUrl}/actas?cedula=V{$cedula}";
        
        try {
            $response = file_get_contents($url);
            
            if ($response === false) {
                return null;
            }
            
            $data = json_decode($response, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }
            
            return $data;
            
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Procesar datos del centro
     */
    private function processCenterData($data)
    {
        $centerId = $data['CenterID'] ?? null;
        
        if (!$centerId || isset($this->processedCenters[$centerId])) {
            return; // Ya procesado
        }

        $center = [
            'code' => $centerId,
            'old_code' => $data['CenterOldID'] ?? null,
            'name' => $this->cleanString($data['CenterName'] ?? ''),
            'address' => $this->cleanString($data['CenterAddress'] ?? ''),
            'state_name' => $this->cleanString($data['StateName'] ?? ''),
            'municipality_name' => $this->cleanString($data['CountyName'] ?? ''),
            'parish_name' => $this->cleanString($data['ParishName'] ?? ''),
            'state_id' => $data['StateID'] ?? null,
            'municipality_id' => $data['CountyID'] ?? null,
            'parish_id' => $data['ParishID'] ?? null,
            'total_voters' => 0, // Se calculará después
            'total_tables' => 0, // Se calculará después
            'status' => 'active',
            'latitude' => null,
            'longitude' => null,
        ];

        $this->processedCenters[$centerId] = $center;
    }

    /**
     * Limpiar strings
     */
    private function cleanString($string)
    {
        return trim(preg_replace('/\s+/', ' ', $string));
    }

    /**
     * Generar archivo CSV
     */
    private function generateCSV()
    {
        echo "📝 Generando archivo CSV...\n";
        
        $csvContent = "code,old_code,name,address,state_name,municipality_name,parish_name,state_id,municipality_id,parish_id,total_voters,total_tables,status,latitude,longitude\n";
        
        foreach ($this->processedCenters as $center) {
            $csvContent .= sprintf(
                '"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                $center['code'],
                $center['old_code'] ?? '',
                $center['name'],
                $center['address'],
                $center['state_name'],
                $center['municipality_name'],
                $center['parish_name'],
                $center['state_id'] ?? '',
                $center['municipality_id'] ?? '',
                $center['parish_id'] ?? '',
                $center['total_voters'],
                $center['total_tables'],
                $center['status'],
                $center['latitude'] ?? '',
                $center['longitude'] ?? ''
            );
        }
        
        file_put_contents($this->outputFile, $csvContent);
    }

    /**
     * Asegurar que el directorio existe
     */
    private function ensureDirectoryExists()
    {
        $directory = dirname($this->outputFile);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }
}

// Ejecutar el script
if (php_sapi_name() === 'cli') {
    $fetcher = new VotingCentersFetcher();
    $fetcher->fetchAllCenters();
}

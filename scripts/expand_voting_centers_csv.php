<?php

/**
 * Script para expandir el archivo CSV de centros de votación
 * Utiliza la API de vzlapi.com para obtener más datos reales
 */

require_once __DIR__ . '/../vendor/autoload.php';

class VotingCentersExpander
{
    private $baseUrl = 'https://api.vzlapi.com';
    private $inputFile = 'database/seeders/data/voting_centers.csv';
    private $outputFile = 'database/seeders/data/voting_centers_expanded.csv';
    private $processedCenters = [];
    private $newCenters = [];

    public function __construct()
    {
        echo "🇻🇪 Expandiendo base de datos de centros de votación de Venezuela...\n";
    }

    /**
     * Método principal para expandir los centros
     */
    public function expandCenters()
    {
        echo "📊 Cargando centros existentes...\n";
        $this->loadExistingCenters();
        
        echo "🔍 Obteniendo centros adicionales de la API...\n";
        $this->fetchAdditionalCenters();
        
        echo "📝 Generando archivo CSV expandido...\n";
        $this->generateExpandedCSV();
        
        echo "✅ Proceso completado. Total de centros: " . count($this->processedCenters) . "\n";
        echo "📄 Archivo generado: {$this->outputFile}\n";
    }

    /**
     * Cargar centros existentes del CSV
     */
    private function loadExistingCenters()
    {
        if (!file_exists($this->inputFile)) {
            echo "❌ Archivo CSV no encontrado: {$this->inputFile}\n";
            return;
        }

        $handle = fopen($this->inputFile, 'r');
        $header = fgetcsv($handle);
        
        while (($row = fgetcsv($handle)) !== false) {
            $data = array_combine($header, $row);
            $this->processedCenters[$data['code']] = $data;
        }
        
        fclose($handle);
        echo "   📍 Centros existentes cargados: " . count($this->processedCenters) . "\n";
    }

    /**
     * Obtener centros adicionales usando rangos de cédulas estratégicos
     */
    private function fetchAdditionalCenters()
    {
        // Rangos de cédulas más específicos para obtener más centros
        $cedulaRanges = [
            // Caracas y área metropolitana
            ['start' => 1000000, 'end' => 2500000, 'step' => 2500],
            ['start' => 10000000, 'end' => 15000000, 'step' => 5000],
            
            // Estados principales
            ['start' => 3000000, 'end' => 4000000, 'step' => 3000], // Anzoátegui
            ['start' => 4000000, 'end' => 5000000, 'step' => 3000], // Aragua
            ['start' => 5000000, 'end' => 6000000, 'step' => 4000], // Barinas
            ['start' => 6000000, 'end' => 7000000, 'step' => 4000], // Bolívar
            ['start' => 7000000, 'end' => 9000000, 'step' => 3000], // Carabobo
            ['start' => 11000000, 'end' => 12000000, 'step' => 3000], // Lara
            ['start' => 12000000, 'end' => 13000000, 'step' => 4000], // Mérida
            ['start' => 15000000, 'end' => 22000000, 'step' => 5000], // Zulia
            ['start' => 18000000, 'end' => 19000000, 'step' => 2000], // Táchira
        ];

        $totalNewCenters = 0;
        
        foreach ($cedulaRanges as $range) {
            echo "🔄 Procesando rango {$range['start']} - {$range['end']}...\n";
            $newInRange = $this->fetchCentersInRange($range['start'], $range['end'], $range['step']);
            $totalNewCenters += $newInRange;
            
            // Pausa entre rangos para no sobrecargar la API
            sleep(2);
        }
        
        echo "   📍 Nuevos centros encontrados: {$totalNewCenters}\n";
    }

    /**
     * Obtener centros en un rango específico
     */
    private function fetchCentersInRange($start, $end, $step = 1000): int
    {
        $newCentersInRange = 0;
        
        for ($cedula = $start; $cedula <= $end; $cedula += $step) {
            try {
                $response = $this->makeApiRequest($cedula);
                
                if ($response && isset($response['CenterID'])) {
                    if ($this->processCenterData($response)) {
                        $newCentersInRange++;
                    }
                }
                
                // Pausa para no sobrecargar la API
                usleep(200000); // 200ms
                
                if ($cedula % 25000 === 0) {
                    echo "   📍 Procesadas cédulas hasta: {$cedula} - Nuevos centros: {$newCentersInRange}\n";
                }
                
            } catch (Exception $e) {
                // Continuar silenciosamente en caso de error
                continue;
            }
        }
        
        return $newCentersInRange;
    }

    /**
     * Realizar petición a la API
     */
    private function makeApiRequest($cedula)
    {
        $url = "{$this->baseUrl}/actas?cedula=V{$cedula}";
        
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'user_agent' => 'VotingCentersExpander/1.0'
                ]
            ]);
            
            $response = file_get_contents($url, false, $context);
            
            if ($response === false) {
                return null;
            }
            
            $data = json_decode($response, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }
            
            return $data;
            
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Procesar datos del centro
     */
    private function processCenterData($data): bool
    {
        $centerId = $data['CenterID'] ?? null;
        
        if (!$centerId || isset($this->processedCenters[$centerId])) {
            return false; // Ya existe
        }

        $center = [
            'code' => $centerId,
            'old_code' => $data['CenterOldID'] ?? '',
            'name' => $this->cleanString($data['CenterName'] ?? ''),
            'address' => $this->cleanString($data['CenterAddress'] ?? ''),
            'state_name' => $this->cleanString($data['StateName'] ?? ''),
            'municipality_name' => $this->cleanString($data['CountyName'] ?? ''),
            'parish_name' => $this->cleanString($data['ParishName'] ?? ''),
            'state_id' => $data['StateID'] ?? '',
            'municipality_id' => $data['CountyID'] ?? '',
            'parish_id' => $data['ParishID'] ?? '',
            'total_voters' => 0, // Se calculará después
            'total_tables' => 0, // Se calculará después
            'status' => 'active',
            'latitude' => '',
            'longitude' => '',
        ];

        $this->processedCenters[$centerId] = $center;
        $this->newCenters[] = $center;
        
        return true;
    }

    /**
     * Limpiar strings
     */
    private function cleanString($string)
    {
        return trim(preg_replace('/\s+/', ' ', $string));
    }

    /**
     * Generar archivo CSV expandido
     */
    private function generateExpandedCSV()
    {
        $csvContent = "code,old_code,name,address,state_name,municipality_name,parish_name,state_id,municipality_id,parish_id,total_voters,total_tables,status,latitude,longitude\n";
        
        foreach ($this->processedCenters as $center) {
            $csvContent .= sprintf(
                '"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                $center['code'],
                $center['old_code'] ?? '',
                $center['name'],
                $center['address'],
                $center['state_name'],
                $center['municipality_name'],
                $center['parish_name'],
                $center['state_id'] ?? '',
                $center['municipality_id'] ?? '',
                $center['parish_id'] ?? '',
                $center['total_voters'],
                $center['total_tables'],
                $center['status'],
                $center['latitude'] ?? '',
                $center['longitude'] ?? ''
            );
        }
        
        file_put_contents($this->outputFile, $csvContent);
        
        // También actualizar el archivo original
        file_put_contents($this->inputFile, $csvContent);
    }

    /**
     * Mostrar estadísticas
     */
    public function showStats()
    {
        echo "\n📊 ESTADÍSTICAS:\n";
        echo "   📄 Total de centros: " . count($this->processedCenters) . "\n";
        echo "   🆕 Nuevos centros agregados: " . count($this->newCenters) . "\n";
        
        // Estadísticas por estado
        $stateStats = [];
        foreach ($this->processedCenters as $center) {
            $state = $center['state_name'];
            $stateStats[$state] = ($stateStats[$state] ?? 0) + 1;
        }
        
        echo "\n📍 CENTROS POR ESTADO:\n";
        arsort($stateStats);
        foreach ($stateStats as $state => $count) {
            echo "   {$state}: {$count} centros\n";
        }
    }
}

// Ejecutar el script
if (php_sapi_name() === 'cli') {
    $expander = new VotingCentersExpander();
    $expander->expandCenters();
    $expander->showStats();
}

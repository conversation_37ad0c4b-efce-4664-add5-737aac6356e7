<?php

require_once __DIR__ . '/../vendor/autoload.php';

// Configurar Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

echo "🗺️  VERIFICANDO UBICACIONES EN LA BASE DE DATOS\n\n";

echo "📍 ESTADOS:\n";
$states = State::all(['name']);
foreach ($states as $state) {
    echo "   - {$state->name}\n";
}

echo "\n📍 PRIMEROS 10 MUNICIPIOS:\n";
$municipalities = Municipality::with('state')->limit(10)->get();
foreach ($municipalities as $municipality) {
    echo "   - {$municipality->name} ({$municipality->state->name})\n";
}

echo "\n📍 PRIMERAS 10 PARROQUIAS:\n";
$parishes = Parish::with(['municipality.state'])->limit(10)->get();
foreach ($parishes as $parish) {
    echo "   - {$parish->name} ({$parish->municipality->name}, {$parish->municipality->state->name})\n";
}

echo "\n📊 ESTADÍSTICAS:\n";
echo "   - Estados: " . State::count() . "\n";
echo "   - Municipios: " . Municipality::count() . "\n";
echo "   - Parroquias: " . Parish::count() . "\n";

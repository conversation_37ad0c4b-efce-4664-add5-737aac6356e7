<?php

/**
 * <PERSON>ript para generar un archivo CSV completo con centros de votación
 * distribuidos por todos los estados, municipios y parroquias de Venezuela
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Configurar Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

class CompleteVotingCentersGenerator
{
    private $outputFile = 'database/seeders/data/voting_centers.csv';
    private $centers = [];
    private $centerCounter = 1;
    
    // Tipos de centros educativos comunes en Venezuela
    private $centerTypes = [
        'UNIDAD EDUCATIVA NACIONAL',
        'ESCUELA BÁSICA NACIONAL',
        'LICEO BOLIVARIANO',
        'UNIDAD EDUCATIVA BOLIVARIANA',
        'ESCUELA BÁSICA ESTADAL',
        'LICEO NACIONAL',
        'INSTITUTO UNIVERSITARIO',
        'COLEGIO',
        'CENTRO DE EDUCACIÓN INICIAL',
        'ESCUELA TÉCNICA',
        'UNIDAD EDUCATIVA PRIVADA',
        'GRUPO ESCOLAR',
        'CENTRO COMUNITARIO',
        'CASA COMUNAL',
        'BIBLIOTECA PÚBLICA',
        'CENTRO CULTURAL'
    ];
    
    // Nombres comunes para centros educativos
    private $centerNames = [
        'SIMÓN BOLÍVAR', 'JOSÉ ANTONIO PÁEZ', 'ANTONIO JOSÉ DE SUCRE', 'FRANCISCO DE MIRANDA',
        'JOSÉ FÉLIX RIBAS', 'RAFAEL URDANETA', 'LUIS BELTRÁN PRIETO FIGUEROA', 'ANDRÉS BELLO',
        'JOSÉ MARÍA VARGAS', 'EZEQUIEL ZAMORA', 'JOSÉ GREGORIO HERNÁNDEZ', 'TERESA CARREÑO',
        'CECILIO ACOSTA', 'RÓMULO GALLEGOS', 'ARTURO USLAR PIETRI', 'MIGUEL OTERO SILVA',
        'AQUILES NAZOA', 'CÉSAR RENGIFO', 'ARMANDO REVERÓN', 'PEDRO GUAL',
        'JUAN VICENTE GÓMEZ', 'ELEAZAR LÓPEZ CONTRERAS', 'ISAÍAS MEDINA ANGARITA',
        'RÓMULO BETANCOURT', 'RAFAEL CALDERA', 'CARLOS ANDRÉS PÉREZ', 'HUGO CHÁVEZ',
        'JOSÉ ANTONIO ANZOÁTEGUI', 'SANTIAGO MARIÑO', 'MANUEL PIAR', 'JOSÉ TADEO MONAGAS',
        'JOSÉ GREGORIO MONAGAS', 'JUAN CRISÓSTOMO FALCÓN', 'ANTONIO GUZMÁN BLANCO'
    ];

    public function __construct()
    {
        echo "🇻🇪 GENERADOR COMPLETO DE CENTROS DE VOTACIÓN DE VENEZUELA\n";
        echo "📊 Generando centros para todos los estados, municipios y parroquias...\n\n";
    }

    /**
     * Generar archivo CSV completo
     */
    public function generateComplete()
    {
        echo "📍 Obteniendo ubicaciones de la base de datos...\n";
        
        $states = State::with(['municipalities.parishes'])->get();
        
        echo "   - Estados: " . $states->count() . "\n";
        echo "   - Municipios: " . $states->sum(function($state) { return $state->municipalities->count(); }) . "\n";
        echo "   - Parroquias: " . $states->sum(function($state) { 
            return $state->municipalities->sum(function($municipality) { 
                return $municipality->parishes->count(); 
            }); 
        }) . "\n\n";
        
        echo "🏗️  Generando centros de votación...\n";
        
        foreach ($states as $state) {
            $this->generateCentersForState($state);
        }
        
        echo "\n📝 Generando archivo CSV...\n";
        $this->generateCSV();
        
        echo "✅ Proceso completado!\n";
        echo "📄 Archivo: {$this->outputFile}\n";
        echo "📊 Total de centros generados: " . count($this->centers) . "\n";
        
        $this->showStatistics();
    }

    /**
     * Generar centros para un estado
     */
    private function generateCentersForState($state)
    {
        echo "🗺️  Procesando {$state->name}...\n";
        
        $stateCounter = 0;
        
        foreach ($state->municipalities as $municipality) {
            foreach ($municipality->parishes as $parish) {
                // Generar entre 1 y 8 centros por parroquia (dependiendo del tamaño estimado)
                $centersCount = $this->estimateCentersForParish($state->name, $municipality->name, $parish->name);
                
                for ($i = 0; $i < $centersCount; $i++) {
                    $center = $this->generateCenter($state, $municipality, $parish, $i + 1);
                    $this->centers[] = $center;
                    $stateCounter++;
                }
            }
        }
        
        echo "   ✅ {$state->name}: {$stateCounter} centros generados\n";
    }

    /**
     * Estimar número de centros por parroquia (expandido para 15,000+ centros)
     */
    private function estimateCentersForParish($stateName, $municipalityName, $parishName)
    {
        // Capitales y ciudades principales: más centros
        $majorCities = [
            'Caracas', 'Maracaibo', 'Valencia', 'Barquisimeto', 'Maracay', 'Ciudad Guayana',
            'San Cristóbal', 'Maturín', 'Ciudad Bolívar', 'Cumaná', 'Mérida', 'Barcelona',
            'Puerto La Cruz', 'Petare', 'Turmero', 'Guarenas', 'Los Teques', 'Guatire'
        ];

        // Parroquias urbanas importantes
        $urbanParishes = [
            'Catedral', 'Santa Teresa', 'La Pastora', 'San José', 'Altagracia', 'Candelaria',
            'El Recreo', 'Sabana Grande', 'El Paraíso', 'La Vega', 'Macarao', 'Sucre',
            'Libertador', 'Chacao', 'Baruta', 'El Hatillo', 'Petare', 'Leoncio Martínez'
        ];

        // Distrito Capital y áreas metropolitanas: 8-25 centros (expandido)
        if ($stateName === 'Distrito Capital' || $stateName === 'Miranda' || $stateName === 'La Guaira') {
            return rand(8, 25);
        }

        // Estados grandes (Zulia, Carabobo, Lara, Aragua): 5-18 centros (expandido)
        if (in_array($stateName, ['Zulia', 'Carabobo', 'Lara', 'Aragua', 'Anzoátegui', 'Bolívar'])) {
            return rand(5, 18);
        }

        // Ciudades principales: 4-12 centros (expandido)
        foreach ($majorCities as $city) {
            if (stripos($municipalityName, $city) !== false || stripos($parishName, $city) !== false) {
                return rand(4, 12);
            }
        }

        // Parroquias urbanas: 3-10 centros (expandido)
        if (in_array($parishName, $urbanParishes)) {
            return rand(3, 10);
        }

        // Estados medianos: 2-8 centros (expandido)
        if (in_array($stateName, ['Táchira', 'Mérida', 'Portuguesa', 'Monagas', 'Sucre', 'Falcón', 'Yaracuy', 'Cojedes', 'Guárico', 'Trujillo', 'Nueva Esparta'])) {
            return rand(2, 8);
        }

        // Estados pequeños o rurales: 2-5 centros (expandido)
        return rand(2, 5);
    }

    /**
     * Generar un centro de votación
     */
    private function generateCenter($state, $municipality, $parish, $centerNumber)
    {
        // Generar código único del centro
        $stateCode = str_pad($state->id, 2, '0', STR_PAD_LEFT);
        $municipalityCode = str_pad($municipality->id, 2, '0', STR_PAD_LEFT);
        $parishCode = str_pad($parish->id, 3, '0', STR_PAD_LEFT);
        $centerCode = str_pad($centerNumber, 3, '0', STR_PAD_LEFT);
        
        $code = $stateCode . $municipalityCode . $parishCode . $centerCode;
        
        // Generar nombre del centro
        $centerType = $this->centerTypes[array_rand($this->centerTypes)];
        $centerName = $this->centerNames[array_rand($this->centerNames)];
        $name = "{$centerType} {$centerName}";
        
        // Si hay múltiples centros en la misma parroquia, agregar número o variación
        if ($centerNumber > 1) {
            $variations = [
                " N° {$centerNumber}",
                " {$centerNumber}",
                " - SEDE {$centerNumber}",
                " EXTENSIÓN {$centerNumber}"
            ];
            $name .= $variations[array_rand($variations)];
        }
        
        // Generar dirección
        $address = $this->generateAddress($municipality->name, $parish->name, $state->name);
        
        // Generar datos de votantes y mesas (realistas)
        $totalVoters = $this->generateVotersCount($state->name, $municipality->name);
        $totalTables = max(1, intval($totalVoters / 400)); // Aproximadamente 400 votantes por mesa
        
        // Generar coordenadas aproximadas (basadas en ubicación real de Venezuela)
        $coordinates = $this->generateCoordinates($state->name, $municipality->name);
        
        return [
            'code' => $code,
            'old_code' => '',
            'name' => $name,
            'address' => $address,
            'state_name' => $state->name,
            'municipality_name' => $municipality->name,
            'parish_name' => $parish->name,
            'state_id' => $state->id,
            'municipality_id' => $municipality->id,
            'parish_id' => $parish->id,
            'total_voters' => $totalVoters,
            'total_tables' => $totalTables,
            'status' => 'active',
            'latitude' => $coordinates['lat'],
            'longitude' => $coordinates['lng']
        ];
    }

    /**
     * Generar dirección realista
     */
    private function generateAddress($municipality, $parish, $state)
    {
        $streets = [
            'Avenida Bolívar', 'Calle Principal', 'Avenida Libertador', 'Calle Miranda',
            'Avenida Sucre', 'Calle Páez', 'Avenida Universidad', 'Calle Comercio',
            'Avenida 5 de Julio', 'Calle Real', 'Avenida Las Américas', 'Calle Carabobo'
        ];
        
        $sectors = [
            'Centro', 'El Centro', 'Casco Central', 'Zona Industrial', 'Urbanización',
            'Barrio', 'Sector', 'Residencias', 'Villa', 'Conjunto Residencial'
        ];
        
        $street = $streets[array_rand($streets)];
        $sector = $sectors[array_rand($sectors)];
        $number = rand(1, 999);
        
        return "{$street} N° {$number}, {$sector} {$parish}, {$municipality}, {$state}";
    }

    /**
     * Generar número de votantes realista
     */
    private function generateVotersCount($stateName, $municipalityName)
    {
        // Centros en áreas metropolitanas: más votantes
        if ($stateName === 'Distrito Capital' || $stateName === 'Miranda') {
            return rand(800, 3500);
        }
        
        // Estados grandes
        if (in_array($stateName, ['Zulia', 'Carabobo', 'Lara', 'Aragua'])) {
            return rand(600, 2800);
        }
        
        // Estados medianos
        if (in_array($stateName, ['Anzoátegui', 'Bolívar', 'Táchira', 'Mérida'])) {
            return rand(400, 2200);
        }
        
        // Estados pequeños o rurales
        return rand(200, 1500);
    }

    /**
     * Generar coordenadas aproximadas
     */
    private function generateCoordinates($stateName, $municipalityName)
    {
        // Coordenadas aproximadas por estado (centro geográfico)
        $stateCoordinates = [
            'Distrito Capital' => ['lat' => 10.5, 'lng' => -66.9],
            'Miranda' => ['lat' => 10.3, 'lng' => -66.8],
            'Zulia' => ['lat' => 10.0, 'lng' => -72.0],
            'Carabobo' => ['lat' => 10.2, 'lng' => -68.0],
            'Lara' => ['lat' => 10.1, 'lng' => -69.3],
            'Aragua' => ['lat' => 10.2, 'lng' => -67.6],
            'Anzoátegui' => ['lat' => 9.0, 'lng' => -64.0],
            'Bolívar' => ['lat' => 7.0, 'lng' => -63.0],
            'Táchira' => ['lat' => 7.8, 'lng' => -72.2],
            'Mérida' => ['lat' => 8.6, 'lng' => -71.2],
            'Portuguesa' => ['lat' => 9.0, 'lng' => -69.7],
            'Monagas' => ['lat' => 9.7, 'lng' => -63.2],
            'Sucre' => ['lat' => 10.5, 'lng' => -64.2],
            'Falcón' => ['lat' => 11.4, 'lng' => -69.7],
            'Yaracuy' => ['lat' => 10.3, 'lng' => -68.7],
            'Cojedes' => ['lat' => 9.7, 'lng' => -68.6],
            'Guárico' => ['lat' => 9.9, 'lng' => -67.4],
            'Trujillo' => ['lat' => 9.4, 'lng' => -70.4],
            'Nueva Esparta' => ['lat' => 11.0, 'lng' => -63.8],
            'La Guaira' => ['lat' => 10.6, 'lng' => -66.9],
            'Barinas' => ['lat' => 8.6, 'lng' => -70.2],
            'Apure' => ['lat' => 7.9, 'lng' => -67.5],
            'Delta Amacuro' => ['lat' => 8.9, 'lng' => -62.1],
            'Amazonas' => ['lat' => 5.7, 'lng' => -67.6]
        ];
        
        $baseCoords = $stateCoordinates[$stateName] ?? ['lat' => 8.0, 'lng' => -66.0];
        
        // Agregar variación aleatoria para simular ubicaciones dentro del estado
        $latVariation = (rand(-100, 100) / 1000); // ±0.1 grados
        $lngVariation = (rand(-100, 100) / 1000); // ±0.1 grados
        
        return [
            'lat' => round($baseCoords['lat'] + $latVariation, 6),
            'lng' => round($baseCoords['lng'] + $lngVariation, 6)
        ];
    }

    /**
     * Generar archivo CSV
     */
    private function generateCSV()
    {
        $csvContent = "code,old_code,name,address,state_name,municipality_name,parish_name,state_id,municipality_id,parish_id,total_voters,total_tables,status,latitude,longitude\n";
        
        foreach ($this->centers as $center) {
            $csvContent .= sprintf(
                '"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                $center['code'],
                $center['old_code'],
                $center['name'],
                $center['address'],
                $center['state_name'],
                $center['municipality_name'],
                $center['parish_name'],
                $center['state_id'],
                $center['municipality_id'],
                $center['parish_id'],
                $center['total_voters'],
                $center['total_tables'],
                $center['status'],
                $center['latitude'],
                $center['longitude']
            );
        }
        
        file_put_contents($this->outputFile, $csvContent);
    }

    /**
     * Mostrar estadísticas
     */
    private function showStatistics()
    {
        echo "\n📊 ESTADÍSTICAS GENERADAS:\n";
        
        // Agrupar por estado
        $stateStats = [];
        $totalVoters = 0;
        $totalTables = 0;
        
        foreach ($this->centers as $center) {
            $state = $center['state_name'];
            if (!isset($stateStats[$state])) {
                $stateStats[$state] = ['centers' => 0, 'voters' => 0, 'tables' => 0];
            }
            $stateStats[$state]['centers']++;
            $stateStats[$state]['voters'] += $center['total_voters'];
            $stateStats[$state]['tables'] += $center['total_tables'];
            
            $totalVoters += $center['total_voters'];
            $totalTables += $center['total_tables'];
        }
        
        echo "🇻🇪 RESUMEN NACIONAL:\n";
        echo "   📍 Total centros: " . number_format(count($this->centers)) . "\n";
        echo "   👥 Total votantes: " . number_format($totalVoters) . "\n";
        echo "   🗳️  Total mesas: " . number_format($totalTables) . "\n\n";
        
        echo "📊 POR ESTADO:\n";
        arsort($stateStats);
        foreach ($stateStats as $state => $stats) {
            echo sprintf("   %s: %s centros, %s votantes, %s mesas\n",
                $state,
                number_format($stats['centers']),
                number_format($stats['voters']),
                number_format($stats['tables'])
            );
        }
    }
}

// Ejecutar el generador
if (php_sapi_name() === 'cli') {
    $generator = new CompleteVotingCentersGenerator();
    $generator->generateComplete();
}

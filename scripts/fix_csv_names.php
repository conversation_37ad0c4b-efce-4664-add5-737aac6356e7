<?php

/**
 * Script para corregir los nombres en el CSV para que coincidan con la base de datos
 */

$csvFile = __DIR__ . '/../database/seeders/data/voting_centers.csv';

// Mapeo de nombres incorrectos a nombres correctos
$stateMapping = [
    'DTTO. CAPITAL' => 'Distrito Capital',
    'EDO. ANZOATEGUI' => 'Anzoátegui',
    'EDO. APURE' => 'Apure',
    'EDO. ARAGUA' => 'Aragua',
    'EDO. BARINAS' => 'Barinas',
    'EDO. BOLIVAR' => 'Bolívar',
    'EDO. CARABOBO' => 'Carabobo',
    'EDO. COJEDES' => 'Cojedes',
    'EDO. FALCON' => 'Falcón',
    'EDO. GUARICO' => 'Guárico',
    'EDO. LARA' => 'Lara',
    'EDO. MERIDA' => '<PERSON><PERSON>rida',
    'EDO. MIRANDA' => '<PERSON>',
    'EDO. MONAGAS' => 'Monagas',
    'EDO. PORTUGUESA' => 'Portuguesa',
    'EDO. SUCRE' => 'Sucre',
    'EDO. TACHIRA' => 'Táchira',
    'EDO. TRUJILLO' => 'Trujillo',
    'EDO. LA GUAIRA' => 'La Guaira',
    'EDO. ZULIA' => 'Zulia',
    'EDO. YARACUY' => 'Yaracuy',
    'EDO. NUEVA ESPARTA' => 'Nueva Esparta',
    'EDO. DELTA AMACURO' => 'Delta Amacuro',
    'EDO. AMAZONAS' => 'Amazonas',
];

$municipalityMapping = [
    'MP. BLVNO LIBERTADOR' => 'Libertador',
    'MP. SIMÓN BOLÍVAR' => 'Bolívar',
    'MP. JUAN ANTONIO SOTILLO' => 'Juan Antonio Sotillo',
    'MP. SIMÓN RODRÍGUEZ' => 'Simón Rodríguez',
    'MP. ACHAGUAS' => 'Achaguas',
    'MP. GIRARDOT' => 'Girardot',
    'MP. SANTIAGO MARIÑO' => 'Santiago Mariño',
    'MP. JOSÉ FÉLIX RIBAS' => 'José Félix Ribas',
    'MP. ROJAS' => 'Rojas',
    'MP. SOSA' => 'Sosa',
    'MP. CARONI' => 'Caroní',
    'MP. DIEGO IBARRA' => 'Diego Ibarra',
    'MP. GUACARA' => 'Guacara',
    'MP. VALENCIA' => 'Valencia',
    'MP. EZEQUIEL ZAMORA' => 'Ezequiel Zamora',
    'MP. MIRANDA' => 'Miranda',
    'MP. JUAN GERMAN ROSCIO' => 'Juan Germán Roscio',
    'MP. IRIBARREN' => 'Iribarren',
    'MP. PALAVECINO' => 'Palavecino',
    'MP. TORRES' => 'Torres',
    'MP. LIBERTADOR' => 'Libertador',
    'MP. SUCRE' => 'Sucre',
    'MP. ACEVEDO' => 'Acevedo',
    'MP. BARUTA' => 'Baruta',
    'MP. MATURÍN' => 'Maturín',
    'MP. GUANARE' => 'Guanare',
    'MP. SAN CRISTÓBAL' => 'San Cristóbal',
    'MP. BOLIVAR' => 'Bolívar',
    'MP. VARGAS' => 'Vargas',
    'MP. MARACAIBO' => 'Maracaibo',
    'MP. GUAJIRA' => 'Guajira',
    'MP. CATATUMBO' => 'Catatumbo',
    'MP. SAN FRANCISCO' => 'San Francisco',
    'MP. JESUS MARIA SEMPRUM' => 'Jesús María Semprún',
    'MP. FRANCISCO JAVIER PULGAR' => 'Francisco Javier Pulgar',
    'MP. SIMON BOLIVAR' => 'Simón Bolívar',
    'MP. SAN FERNANDO' => 'San Fernando',
    'MP. ATURES' => 'Atures',
    'MP. CHACAO' => 'Chacao',
    'MP. EL HATILLO' => 'El Hatillo',
];

$parishMapping = [
    'PQ. ALTAGRACIA' => 'Altagracia',
    'PQ. CANDELARIA' => 'Candelaria',
    'PQ. SUCRE' => 'Sucre',
    'PQ. MACARAO' => 'Macarao',
    'PQ. BARCELONA' => 'Barcelona',
    'PQ. PUERTO LA CRUZ' => 'Puerto La Cruz',
    'PQ. EL TIGRE' => 'El Tigre',
    'PQ. ACHAGUAS' => 'Achaguas',
    'PQ. MARACAY' => 'Maracay',
    'PQ. TURMERO' => 'Turmero',
    'PQ. LA VICTORIA' => 'La Victoria',
    'PQ. LIBERTAD' => 'Libertad',
    'PQ. PUERTO DE NUTRIAS' => 'Puerto de Nutrias',
    'PQ. UNARE' => 'Unare',
    'PQ. MARIARA' => 'Mariara',
    'PQ. GUACARA' => 'Guacara',
    'PQ. SAN BLAS' => 'San Blas',
    'PQ. SAN JOSE' => 'San José',
    'PQ. SAN CARLOS' => 'San Carlos',
    'PQ. SAN GABRIEL' => 'San Gabriel',
    'PQ. SAN JUAN DE LOS MORROS' => 'San Juan de los Morros',
    'PQ. CATEDRAL' => 'Catedral',
    'PQ. JOSE G. BASTIDAS' => 'José Gregorio Bastidas',
    'PQ. TRINIDAD SAMUEL' => 'Trinidad Samuel',
    'PQ. ARIAS' => 'Arias',
    'PQ. SAN JUAN' => 'San Juan',
    'PQ. CAUCAGUA' => 'Caucagua',
    'PQ. PETARE' => 'Petare',
    'PQ. LAS MINAS DE BARUTA' => 'Las Minas de Baruta',
    'PQ. MATURÍN' => 'Maturín',
    'PQ. GUANARE' => 'Guanare',
    'PQ. SANTA INÉS' => 'Santa Inés',
    'PQ. SAN CRISTÓBAL' => 'San Cristóbal',
    'PQ. LA CONCORDIA' => 'La Concordia',
    'PQ. CHEREGUE' => 'Cheregüé',
    'PQ. LA GUAIRA' => 'La Guaira',
    'PQ. VENANCIO PULGAR' => 'Venancio Pulgar',
    'PQ. GUAJIRA' => 'Guajira',
    'PQ. SINAMAICA' => 'Sinamaica',
    'PQ. UDON PEREZ' => 'Udón Pérez',
    'PQ. SAN FRANCISCO' => 'San Francisco',
    'PQ. BARI' => 'Barí',
    'PQ. CARLOS QUEVEDO' => 'Carlos Quevedo',
    'PQ. RAFAEL URDANETA' => 'Rafael Urdaneta',
    'PQ. CARABALLEDA' => 'Caraballeda',
    'PQ. MACUTO' => 'Macuto',
    'PQ. MAIQUETIA' => 'Maiquetía',
    'PQ. SAN FERNANDO' => 'San Fernando',
    'PQ. FERNANDO GIRÓN TOVAR' => 'Fernando Girón Tovar',
    'PQ. CHACAO' => 'Chacao',
    'PQ. EL HATILLO' => 'El Hatillo',
    'PQ. BARUTA' => 'Baruta',
    'PQ. LEONCIO MARTÍNEZ' => 'Leoncio Martínez',
];

echo "🔧 Corrigiendo nombres en el archivo CSV...\n";

if (!file_exists($csvFile)) {
    echo "❌ Archivo CSV no encontrado: {$csvFile}\n";
    exit(1);
}

// Leer el archivo
$content = file_get_contents($csvFile);

// Aplicar correcciones
foreach ($stateMapping as $old => $new) {
    $content = str_replace('"' . $old . '"', '"' . $new . '"', $content);
}

foreach ($municipalityMapping as $old => $new) {
    $content = str_replace('"' . $old . '"', '"' . $new . '"', $content);
}

foreach ($parishMapping as $old => $new) {
    $content = str_replace('"' . $old . '"', '"' . $new . '"', $content);
}

// Guardar el archivo corregido
file_put_contents($csvFile, $content);

echo "✅ Archivo CSV corregido exitosamente!\n";
echo "📄 Archivo: {$csvFile}\n";

// Mostrar algunas líneas de muestra
echo "\n📋 Muestra del archivo corregido:\n";
$lines = explode("\n", $content);
for ($i = 0; $i < min(5, count($lines)); $i++) {
    echo "   " . ($i + 1) . ": " . substr($lines[$i], 0, 100) . "...\n";
}

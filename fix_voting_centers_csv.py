import pandas as pd
import math
import random

def map_state_name(excel_state):
    """Map Excel state names to exact database state names"""
    state_mapping = {
        'CAPITAL': 'Distrito Capital',
        'ANZOATEGUI': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        'APURE': 'Apure',
        'ARAGUA': 'Aragua',
        'BARINAS': '<PERSON>nas',
        'BOLIVAR': '<PERSON><PERSON><PERSON><PERSON>',
        'CARABOBO': 'Carabobo',
        'COJEDES': 'Coje<PERSON>',
        'FALCON': 'Falcón',
        'GUARICO': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        'LARA': 'Lara',
        'MERIDA': '<PERSON><PERSON><PERSON>',
        'MIRANDA': '<PERSON>',
        'MONAGAS': 'Mona<PERSON>',
        'NUEVA ESPARTA': 'Nueva Esparta',
        'PORTUGUESA': 'Portuguesa',
        'SUCRE': 'Sucre',
        'TACHIRA': 'Táchira',
        'TRUJILLO': '<PERSON>ruji<PERSON>',
        'YARACUY': 'Ya<PERSON><PERSON>',
        'ZULIA': '<PERSON>uli<PERSON>',
        'AMAZONAS': 'Amazonas',
        'DELTA AMACURO': 'Delta Amacuro',
        'LA GUAIRA': 'La Guaira',
        'DEPENDENCIAS FEDERALES': 'Dependencias Federales'
    }
    return state_mapping.get(excel_state.upper(), excel_state)

def map_municipality_name(excel_municipality, state_name):
    """Map Excel municipality names to exact database municipality names"""
    # Remove common prefixes and clean the name
    clean_name = str(excel_municipality).strip()
    prefixes_to_remove = ['MP. ', 'MUNICIPIO ', 'MUN. ']
    for prefix in prefixes_to_remove:
        if clean_name.upper().startswith(prefix):
            clean_name = clean_name[len(prefix):].strip()
    
    # Convert to title case for most cases
    clean_name = clean_name.title()
    
    # Specific mappings for known differences
    municipality_mappings = {
        # Amazonas
        'Alto Orinoco': 'Alto Orinoco',
        'Atabapo': 'Atabapo', 
        'Atures': 'Atures',
        'Autana': 'Autana',
        'Manapiare': 'Manapiare',
        'Maroa': 'Maroa',
        'Rio Negro': 'Río Negro',
        # Anzoátegui
        'Anaco': 'Anaco',
        'Aragua': 'Aragua',
        'Bolivar': 'Bolívar',
        'Bruzual': 'Bruzual',
        'Cajigal': 'Cajigal',
        'Freites': 'Freites',
        'Independencia': 'Independencia',
        'Libertad': 'Libertad',
        'Mcgregor': 'McGregor',
        'Miranda': 'Miranda',
        'Monagas': 'Monagas',
        'Peñalver': 'Peñalver',
        'Piritu': 'Píritu',
        'San Juan De Capistrano': 'San Juan de Capistrano',
        'Santa Ana': 'Santa Ana',
        'Simon Bolivar': 'Simón Bolívar',
        'Simon Rodriguez': 'Simón Rodríguez',
        'Sir Arthur Mcgregor': 'Sir Arthur McGregor',
        'Sotillo': 'Sotillo',
        'Turismo': 'Turismo',
        'Urbaneja': 'Urbaneja',
        'Guanta': 'Guanta',
        'Puerto Piritu': 'Puerto Píritu',
        'El Tigre': 'El Tigre',
        'Pao': 'Pao',
        'San Jose De Guanipa': 'San José de Guanipa',
    }
    
    return municipality_mappings.get(clean_name, clean_name)

def map_parish_name(excel_parish, municipality_name, state_name):
    """Map Excel parish names to exact database parish names"""
    # Remove common prefixes and clean the name
    clean_name = str(excel_parish).strip()
    prefixes_to_remove = ['PQ. ', 'PARROQUIA ']
    for prefix in prefixes_to_remove:
        if clean_name.upper().startswith(prefix):
            clean_name = clean_name[len(prefix):].strip()
    
    # Convert to title case for most cases
    clean_name = clean_name.title()
    
    # Specific mappings for known parishes
    parish_mappings = {
        # Amazonas - Alto Orinoco
        'La Esmeralda': 'La Esmeralda',
        'Huachamacare': 'Huachamacare', 
        'Marawaka': 'Marawaka',
        'Mavaca': 'Mavaca',
        'Sierra Parima': 'Sierra Parima',
        # Amazonas - Atabapo
        'San Fernando De Atabapo': 'San Fernando de Atabapo',
        'Ucata': 'Ucata',
        'Yapacana': 'Yapacana',
        'Caname': 'Caname',
        # Amazonas - Atures
        'Fernando Giron Tovar': 'Fernando Girón Tovar',
        'Luis Alberto Gomez': 'Luis Alberto Gómez',
        'Parhueña': 'Pahueña',
        'Platanillal': 'Platanillal',
        # Add more mappings as needed
    }
    
    return parish_mappings.get(clean_name, clean_name)

def calculate_tables(total_voters):
    """Calculate number of voting tables based on total voters"""
    if total_voters <= 0:
        return 1
    # Approximately 400-500 voters per table
    tables = math.ceil(total_voters / 450)
    return max(1, tables)

def generate_random_coordinates():
    """Generate random coordinates within Venezuela's approximate bounds"""
    # Venezuela approximate bounds
    lat_min, lat_max = 0.5, 12.5
    lon_min, lon_max = -73.5, -59.5
    
    latitude = round(random.uniform(lat_min, lat_max), 3)
    longitude = round(random.uniform(lon_min, lon_max), 3)
    
    return latitude, longitude

def main():
    print("🔧 Corrigiendo archivo CSV de centros de votación...")
    
    # Read the Excel file
    df = pd.read_excel('database/seeders/data/centros.xlsx')
    
    # Remove unnamed columns and clean data
    df_clean = df.iloc[:, :13].copy()
    
    print(f"📊 Total voting centers in Excel: {len(df_clean)}")
    
    # Clean and prepare data with correct mappings
    output_data = []
    
    for idx, row in df_clean.iterrows():
        # Map names to database format
        state_name = map_state_name(row['estado_corto'])
        municipality_name = map_municipality_name(row['municipio_corto'], state_name)
        parish_name = map_parish_name(row['parroquia_corto'], municipality_name, state_name)
        
        # Calculate tables
        total_voters = int(row['electores_total']) if pd.notna(row['electores_total']) else 0
        total_tables = calculate_tables(total_voters)
        
        # Generate coordinates
        latitude, longitude = generate_random_coordinates()
        
        # Generate a simple sequential code for now
        code = f"VE{idx+1:06d}"
        
        # Create output record
        output_record = {
            'code': code,
            'old_code': '',  # Empty for new data
            'name': str(row['centro']).strip() if pd.notna(row['centro']) else f'Centro {code}',
            'address': f"Centro de Votación {row['centro']}, {parish_name}, {municipality_name}, {state_name}" if pd.notna(row['centro']) else '',
            'state_name': state_name,
            'municipality_name': municipality_name,
            'parish_name': parish_name,
            'total_voters': total_voters,
            'total_tables': total_tables,
            'status': 'active',
            'latitude': latitude,
            'longitude': longitude
        }
        
        output_data.append(output_record)
        
        if len(output_data) % 1000 == 0:
            print(f"✅ Processed {len(output_data)} centers...")
    
    # Create output DataFrame
    output_df = pd.DataFrame(output_data)
    
    # Sort by state, municipality, parish, and code
    output_df = output_df.sort_values(['state_name', 'municipality_name', 'parish_name', 'code'])
    
    # Save to CSV
    output_file = 'database/seeders/data/voting_centers_fixed.csv'
    output_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"\n🎉 Corrección completada exitosamente!")
    print(f"📄 Output file: {output_file}")
    print(f"📊 Total centers processed: {len(output_df)}")
    print(f"🗺️  States: {output_df['state_name'].nunique()}")
    print(f"🏛️  Municipalities: {output_df['municipality_name'].nunique()}")
    print(f"🏘️  Parishes: {output_df['parish_name'].nunique()}")
    print(f"👥 Total voters: {output_df['total_voters'].sum():,}")
    print(f"🗳️  Total tables: {output_df['total_tables'].sum():,}")
    
    # Show sample data
    print(f"\n📋 Sample of corrected data:")
    print(output_df[['code', 'name', 'state_name', 'municipality_name', 'parish_name', 'total_voters', 'total_tables']].head(10))
    
    # Show unique states to verify mapping
    print(f"\n🗺️ Estados mapeados:")
    for state in sorted(output_df['state_name'].unique()):
        print(f"   - {state}")

if __name__ == "__main__":
    main()

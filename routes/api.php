<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\VotingCenterController;
use App\Http\Controllers\Api\ElectoralApiController;
use App\Http\Controllers\ReportsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Voting Centers API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('voting-centers')->group(function () {
    // Rutas públicas (sin autenticación)
    Route::get('/', [VotingCenterController::class, 'index']);
    Route::get('/search', [VotingCenterController::class, 'searchByCode']);
    Route::get('/advanced-search', [VotingCenterController::class, 'advancedSearch']);
    Route::get('/nearby', [VotingCenterController::class, 'nearby']);
    Route::get('/statistics', [VotingCenterController::class, 'statistics']);
    Route::get('/export', [VotingCenterController::class, 'export']);

    // Rutas por ubicación
    Route::get('/state/{state}', [VotingCenterController::class, 'byState']);
    Route::get('/municipality/{municipality}', [VotingCenterController::class, 'byMunicipality']);
    Route::get('/parish/{parish}', [VotingCenterController::class, 'byParish']);

    // Rutas específicas
    Route::get('/{votingCenter}', [VotingCenterController::class, 'show']);

    // Rutas protegidas (requieren autenticación)
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/', [VotingCenterController::class, 'store']);
        Route::put('/{votingCenter}', [VotingCenterController::class, 'update']);
        Route::patch('/bulk-update', [VotingCenterController::class, 'bulkUpdate']);
        Route::delete('/{votingCenter}', [VotingCenterController::class, 'destroy']);
    });
});

/*
|--------------------------------------------------------------------------
| Location API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('locations')->group(function () {
    // Estados
    Route::get('/states', function () {
        return response()->json([
            'success' => true,
            'data' => \App\Models\State::orderBy('name')->get(),
            'message' => 'Estados obtenidos exitosamente'
        ]);
    });
    
    // Municipios por estado
    Route::get('/states/{state}/municipalities', function (\App\Models\State $state) {
        return response()->json([
            'success' => true,
            'data' => $state->municipalities()->orderBy('name')->get(),
            'message' => "Municipios del estado {$state->name}"
        ]);
    });
    
    // Parroquias por municipio
    Route::get('/municipalities/{municipality}/parishes', function (\App\Models\Municipality $municipality) {
        return response()->json([
            'success' => true,
            'data' => $municipality->parishes()->orderBy('name')->get(),
            'message' => "Parroquias del municipio {$municipality->name}"
        ]);
    });
    
    // Todas las ubicaciones jerárquicas
    Route::get('/hierarchy', function () {
        $states = \App\Models\State::with(['municipalities.parishes'])
            ->orderBy('name')
            ->get();
            
        return response()->json([
            'success' => true,
            'data' => $states,
            'message' => 'Jerarquía de ubicaciones obtenida exitosamente'
        ]);
    });
});

/*
|--------------------------------------------------------------------------
| Electoral Data API Routes (Enhanced)
|--------------------------------------------------------------------------
*/

Route::prefix('electoral')->group(function () {
    // Información electoral por cédula
    Route::get('/citizen/{cedula}', [ElectoralApiController::class, 'getCitizenInfo']);

    // Centros por área geográfica
    Route::get('/centers/area', [ElectoralApiController::class, 'getCentersByArea']);

    // Búsqueda avanzada de centros
    Route::get('/centers/search', [ElectoralApiController::class, 'searchCenters']);

    // Centros cercanos por coordenadas
    Route::get('/centers/nearby', [ElectoralApiController::class, 'getNearbyVotingCenters']);

    // Estadísticas electorales
    Route::get('/statistics', [ElectoralApiController::class, 'getElectoralStats']);
});

/*
|--------------------------------------------------------------------------
| Reports API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('reports')->group(function () {
    // Reporte electoral completo
    Route::get('/electoral', [ReportsController::class, 'electoralReport']);

    // Reporte de distribución de centros
    Route::get('/distribution', [ReportsController::class, 'distributionReport']);

    // Análisis de capacidad
    Route::get('/capacity', [ReportsController::class, 'capacityReport']);

    // Reporte geográfico
    Route::get('/geographic', [ReportsController::class, 'geographicReport']);
});

/*
|--------------------------------------------------------------------------
| Health Check Routes
|--------------------------------------------------------------------------
*/

Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'version' => '1.0.0',
        'database' => [
            'states' => \App\Models\State::count(),
            'municipalities' => \App\Models\Municipality::count(),
            'parishes' => \App\Models\Parish::count(),
            'voting_centers' => \App\Models\VotingCenter::count(),
        ]
    ]);
});

Route::get('/ping', function () {
    return response()->json(['message' => 'pong']);
});
